2025-06-30 19:01:26 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-30 19:02:26 - DownloadService - INFO - 准备文件夹下载: folder_id=6, user_id=1, request_context={'session_id': None, 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'download_source': 'web'}
2025-06-30 19:02:27 - DownloadService - INFO - 文件夹下载包含 2 个需要加密的文件
2025-06-30 19:02:27 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-06-30 19:02:44 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-06-30 19:03:01 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\folder_123_20250630_190227.zip
2025-06-30 19:03:01 - DownloadService - INFO - 创建下载批次成功: 9b48b3d5-34b9-4f96-a3a1-21df326530c9, 类型=folder, 目标=folder:6
2025-06-30 19:03:01 - DownloadService - INFO - 批次状态更新成功: 9b48b3d5-34b9-4f96-a3a1-21df326530c9 -> ready
2025-06-30 19:03:01 - DownloadService - INFO - 记录下载成功: 文件夹ID=6, 用户ID=1, 类型=folder, 批次=9b48b3d5-34b9-4f96-a3a1-21df326530c9, 加密=True
2025-06-30 19:03:01 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=367, 用户ID=1
2025-06-30 19:03:01 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=370, 用户ID=1
2025-06-30 19:03:22 - DownloadService - INFO - 准备文件夹下载: folder_id=6, user_id=1, request_context={'session_id': None, 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'download_source': 'web'}
2025-06-30 19:03:22 - DownloadService - INFO - 文件夹下载包含 2 个需要加密的文件
2025-06-30 19:03:22 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-06-30 19:03:40 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-06-30 19:03:58 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\folder_123_20250630_190322.zip
2025-06-30 19:03:58 - DownloadService - INFO - 创建下载批次成功: 97092fd9-62a5-46bc-8c0a-1755fbf40a19, 类型=folder, 目标=folder:6
2025-06-30 19:03:58 - DownloadService - INFO - 批次状态更新成功: 97092fd9-62a5-46bc-8c0a-1755fbf40a19 -> ready
2025-06-30 19:03:58 - DownloadService - INFO - 记录下载成功: 文件夹ID=6, 用户ID=1, 类型=folder, 批次=97092fd9-62a5-46bc-8c0a-1755fbf40a19, 加密=True
2025-06-30 19:03:58 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=367, 用户ID=1
2025-06-30 19:03:58 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=370, 用户ID=1
2025-06-30 19:10:17 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-30 19:10:23 - DownloadService - INFO - 准备文件夹下载: folder_id=6, user_id=1, request_context={'session_id': None, 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'download_source': 'web'}
2025-06-30 19:10:24 - DownloadService - INFO - 文件夹下载包含 2 个需要加密的文件
2025-06-30 19:10:24 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-06-30 19:10:41 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-06-30 19:10:58 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\folder_123_20250630_191024.zip
2025-06-30 19:10:58 - DownloadService - INFO - 创建下载批次成功: 2b04e890-bc8b-4d7a-96a8-9df118fabbf5, 类型=folder, 目标=folder:6
2025-06-30 19:10:58 - DownloadService - INFO - 批次状态更新成功: 2b04e890-bc8b-4d7a-96a8-9df118fabbf5 -> ready
2025-06-30 19:10:58 - DownloadService - INFO - 记录下载成功: 文件夹ID=6, 用户ID=1, 类型=folder, 批次=2b04e890-bc8b-4d7a-96a8-9df118fabbf5, 加密=True
2025-06-30 19:10:58 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=367, 用户ID=1
2025-06-30 19:10:58 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=370, 用户ID=1
2025-06-30 19:11:59 - DownloadService - INFO - 准备文件夹下载: folder_id=7, user_id=1, request_context={'session_id': None, 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'download_source': 'web'}
2025-06-30 19:12:09 - DownloadService - INFO - 创建下载批次成功: c14cae9d-ca83-478b-8a34-7059b48b113f, 类型=folder, 目标=folder:7
2025-06-30 19:12:09 - DownloadService - INFO - 批次状态更新成功: c14cae9d-ca83-478b-8a34-7059b48b113f -> ready
2025-06-30 19:12:09 - DownloadService - INFO - 记录下载成功: 文件夹ID=7, 用户ID=1, 类型=folder, 批次=c14cae9d-ca83-478b-8a34-7059b48b113f, 加密=False
2025-06-30 19:12:41 - DownloadService - INFO - 创建下载批次成功: fb02db90-ccd0-4d8c-922e-e2219e17e012, 类型=batch, 目标=file:None
2025-06-30 19:12:41 - DownloadService - INFO - 批次状态更新成功: fb02db90-ccd0-4d8c-922e-e2219e17e012 -> ready
2025-06-30 19:12:41 - DownloadService - INFO - 记录下载成功: 文件夹ID=None, 用户ID=1, 类型=batch, 批次=fb02db90-ccd0-4d8c-922e-e2219e17e012, 加密=False
2025-06-30 19:24:03 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
