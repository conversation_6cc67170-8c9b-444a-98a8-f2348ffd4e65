2025-07-01 07:57:17 - UserService - INFO - 用户登录成功: admin
2025-07-01 07:57:26 - UserService - INFO - 用户登录成功: admin
2025-07-01 08:27:04 - UserService - INFO - 用户登录成功: admin
2025-07-01 09:17:11 - UserService - INFO - 用户登录成功: admin
2025-07-01 10:35:42 - UserService - INFO - 用户登录成功: admin
2025-07-01 11:09:04 - UserService - INFO - 用户登录成功: admin
2025-07-01 11:16:54 - UserService - INFO - 用户登录成功: admin
2025-07-01 11:20:49 - UserService - INFO - 用户登录成功: admin
2025-07-01 11:21:48 - UserService - INFO - 用户登录成功: admin
2025-07-01 11:26:35 - UserService - INFO - 用户登录成功: admin
2025-07-01 15:24:27 - UserService - INFO - 用户登录成功: admin
2025-07-01 15:44:06 - UserService - INFO - 用户登录成功: admin
2025-07-01 15:54:36 - UserService - INFO - 用户登录成功: admin
2025-07-01 16:10:00 - UserService - INFO - 用户登录成功: admin
2025-07-01 16:35:17 - UserService - INFO - 用户登录成功: admin
2025-07-01 16:39:08 - UserService - INFO - 用户登录成功: admin
2025-07-01 16:40:29 - UserService - INFO - 用户登录成功: admin
2025-07-01 16:47:35 - UserService - INFO - 用户登录成功: admin
2025-07-01 16:48:42 - UserService - INFO - 用户登录成功: admin
2025-07-01 16:53:15 - UserService - ERROR - 删除用户失败: (sqlite3.OperationalError) no such column: user_sessions.total_page_views
[SQL: SELECT user_sessions.id AS user_sessions_id, user_sessions.user_id AS user_sessions_user_id, user_sessions.session_id AS user_sessions_session_id, user_sessions.ip_address AS user_sessions_ip_address, user_sessions.user_agent AS user_sessions_user_agent, user_sessions.login_method AS user_sessions_login_method, user_sessions.total_page_views AS user_sessions_total_page_views, user_sessions.total_searches AS user_sessions_total_searches, user_sessions.total_downloads AS user_sessions_total_downloads, user_sessions.login_time AS user_sessions_login_time, user_sessions.logout_time AS user_sessions_logout_time, user_sessions.last_activity AS user_sessions_last_activity, user_sessions.is_active AS user_sessions_is_active, user_sessions.logout_reason AS user_sessions_logout_reason 
FROM user_sessions 
WHERE ? = user_sessions.user_id]
[parameters: (2,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-01 16:53:28 - UserService - ERROR - 创建用户失败: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email
[SQL: INSERT INTO users (username, password_hash, salt, email, full_name, is_active, is_admin, is_banned, ban_until, user_group, last_login, login_count, failed_login_attempts, last_failed_login, session_token, session_expires, download_count, upload_count, search_count, created_at, updated_at, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?) RETURNING id, created_at, updated_at]
[parameters: ('fii', 'b59ad4252fb189b99fc8f9cd09a5aed45b7d96ed0783be95b6b41e2d876d3f0e', '753de1dc2046fb2cd3f9861cb4402922', '', '', 1, 0, 0, None, 'user', None, 0, 0, None, None, None, 0, 0, 0, None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-01 16:53:41 - UserService - INFO - 用户创建成功: fii
2025-07-01 16:53:51 - UserService - INFO - 用户登录成功: fii
2025-07-01 16:54:58 - UserService - ERROR - 删除用户失败: (sqlite3.OperationalError) no such column: user_sessions.total_page_views
[SQL: SELECT user_sessions.id AS user_sessions_id, user_sessions.user_id AS user_sessions_user_id, user_sessions.session_id AS user_sessions_session_id, user_sessions.ip_address AS user_sessions_ip_address, user_sessions.user_agent AS user_sessions_user_agent, user_sessions.login_method AS user_sessions_login_method, user_sessions.total_page_views AS user_sessions_total_page_views, user_sessions.total_searches AS user_sessions_total_searches, user_sessions.total_downloads AS user_sessions_total_downloads, user_sessions.login_time AS user_sessions_login_time, user_sessions.logout_time AS user_sessions_logout_time, user_sessions.last_activity AS user_sessions_last_activity, user_sessions.is_active AS user_sessions_is_active, user_sessions.logout_reason AS user_sessions_logout_reason 
FROM user_sessions 
WHERE ? = user_sessions.user_id]
[parameters: (2,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-01 16:55:06 - UserService - ERROR - 删除用户失败: (sqlite3.OperationalError) no such column: user_sessions.total_page_views
[SQL: SELECT user_sessions.id AS user_sessions_id, user_sessions.user_id AS user_sessions_user_id, user_sessions.session_id AS user_sessions_session_id, user_sessions.ip_address AS user_sessions_ip_address, user_sessions.user_agent AS user_sessions_user_agent, user_sessions.login_method AS user_sessions_login_method, user_sessions.total_page_views AS user_sessions_total_page_views, user_sessions.total_searches AS user_sessions_total_searches, user_sessions.total_downloads AS user_sessions_total_downloads, user_sessions.login_time AS user_sessions_login_time, user_sessions.logout_time AS user_sessions_logout_time, user_sessions.last_activity AS user_sessions_last_activity, user_sessions.is_active AS user_sessions_is_active, user_sessions.logout_reason AS user_sessions_logout_reason 
FROM user_sessions 
WHERE ? = user_sessions.user_id]
[parameters: (3,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-01 16:57:07 - UserService - ERROR - 删除用户失败: (sqlite3.OperationalError) no such column: user_sessions.total_page_views
[SQL: SELECT user_sessions.id AS user_sessions_id, user_sessions.user_id AS user_sessions_user_id, user_sessions.session_id AS user_sessions_session_id, user_sessions.ip_address AS user_sessions_ip_address, user_sessions.user_agent AS user_sessions_user_agent, user_sessions.login_method AS user_sessions_login_method, user_sessions.total_page_views AS user_sessions_total_page_views, user_sessions.total_searches AS user_sessions_total_searches, user_sessions.total_downloads AS user_sessions_total_downloads, user_sessions.login_time AS user_sessions_login_time, user_sessions.logout_time AS user_sessions_logout_time, user_sessions.last_activity AS user_sessions_last_activity, user_sessions.is_active AS user_sessions_is_active, user_sessions.logout_reason AS user_sessions_logout_reason 
FROM user_sessions 
WHERE ? = user_sessions.user_id]
[parameters: (2,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-01 16:57:25 - UserService - ERROR - 删除用户失败: (sqlite3.OperationalError) no such column: user_sessions.total_page_views
[SQL: SELECT user_sessions.id AS user_sessions_id, user_sessions.user_id AS user_sessions_user_id, user_sessions.session_id AS user_sessions_session_id, user_sessions.ip_address AS user_sessions_ip_address, user_sessions.user_agent AS user_sessions_user_agent, user_sessions.login_method AS user_sessions_login_method, user_sessions.total_page_views AS user_sessions_total_page_views, user_sessions.total_searches AS user_sessions_total_searches, user_sessions.total_downloads AS user_sessions_total_downloads, user_sessions.login_time AS user_sessions_login_time, user_sessions.logout_time AS user_sessions_logout_time, user_sessions.last_activity AS user_sessions_last_activity, user_sessions.is_active AS user_sessions_is_active, user_sessions.logout_reason AS user_sessions_logout_reason 
FROM user_sessions 
WHERE ? = user_sessions.user_id]
[parameters: (2,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-01 17:00:56 - UserService - ERROR - 删除用户失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_bans.user_id
[SQL: UPDATE user_bans SET user_id=? WHERE user_bans.id = ?]
[parameters: (None, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-01 17:02:22 - UserService - INFO - 用户删除成功: fjj
2025-07-01 17:02:25 - UserService - ERROR - 删除用户失败: (sqlite3.IntegrityError) NOT NULL constraint failed: user_bans.user_id
[SQL: UPDATE user_bans SET user_id=? WHERE user_bans.id = ?]
[parameters: (None, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-01 19:55:36 - UserService - INFO - 用户登录成功: admin
