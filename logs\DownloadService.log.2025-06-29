2025-06-29 20:05:45 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-29 20:06:55 - DownloadService - INFO - 准备文件夹下载: folder_id=3, user_id=1, request_context={'session_id': None, 'ip_address': '127.0.0.1', 'user_agent': 'python-requests/2.31.0', 'download_source': 'web'}
2025-06-29 20:06:55 - DownloadService - INFO - 创建下载批次成功: da7bbabc-5c91-4e6a-9a48-98f420019538, 类型=folder, 目标=folder:3
2025-06-29 20:06:55 - DownloadService - INFO - 批次状态更新成功: da7bbabc-5c91-4e6a-9a48-98f420019538 -> ready
2025-06-29 20:06:55 - DownloadService - INFO - 记录下载成功: 文件夹ID=3, 用户ID=1, 类型=folder, 批次=da7bbabc-5c91-4e6a-9a48-98f420019538, 加密=False
2025-06-29 20:07:10 - DownloadService - INFO - 准备文件夹下载: folder_id=3, user_id=1, request_context={'session_id': None, 'ip_address': '127.0.0.1', 'user_agent': 'python-requests/2.31.0', 'download_source': 'web'}
2025-06-29 20:07:10 - DownloadService - INFO - 创建下载批次成功: 153a42b2-1a51-4ad6-91d5-cb7129564223, 类型=folder, 目标=folder:3
2025-06-29 20:07:10 - DownloadService - INFO - 批次状态更新成功: 153a42b2-1a51-4ad6-91d5-cb7129564223 -> ready
2025-06-29 20:07:10 - DownloadService - INFO - 记录下载成功: 文件夹ID=3, 用户ID=1, 类型=folder, 批次=153a42b2-1a51-4ad6-91d5-cb7129564223, 加密=False
