#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像搜索服务
"""

import os
import hashlib
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from models.file_share import SharedFile, SharedFolder
from models.image_features import (
    ImageFeatureIndex, ImageSearchHistory, ImageSearchCache,
    ImageFeatures, SearchOptions, SearchResult, ImageMatch
)
from utils.logger import setup_logger

# 可选导入图像处理库
try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False

try:
    import cv2
    HAS_OPENCV = True
except ImportError:
    HAS_OPENCV = False

try:
    from PIL import Image
    HAS_PIL = True
except ImportError:
    HAS_PIL = False

class ImageSearchService:
    """图像搜索核心服务"""
    
    def __init__(self, db_manager, config: Dict[str, Any] = None):
        self.db_manager = db_manager
        self.logger = setup_logger("ImageSearchService")
        
        # 默认配置
        self.config = {
            'similarity_threshold': 0.7,
            'max_results': 50,
            'cache_ttl_hours': 24,
            'feature_version': '1.0',
            'supported_formats': {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp'},
            'max_image_size': 800,
            'enable_cache': True,
            'enable_partial_match': True
        }
        
        if config:
            self.config.update(config)
        
        # 检查依赖
        self._check_dependencies()
        
        # 初始化特征提取和相似度计算服务
        self.feature_extractor = None
        self.similarity_calculator = None
        
        self.logger.info("图像搜索服务初始化完成")
    
    def _check_dependencies(self):
        """检查依赖库"""
        missing_deps = []
        
        if not HAS_NUMPY:
            missing_deps.append("numpy")
        if not HAS_OPENCV:
            missing_deps.append("opencv-python")
        if not HAS_PIL:
            missing_deps.append("Pillow")
        
        if missing_deps:
            self.logger.warning(f"缺少依赖库: {', '.join(missing_deps)}")
            self.logger.warning("图像搜索功能将受限，请安装缺少的依赖库")
        else:
            self.logger.info("所有依赖库检查通过")
    
    def search_by_image(self, image_path: str, options: SearchOptions = None) -> SearchResult:
        """通过图像搜索相似图片"""
        start_time = time.time()
        
        try:
            if not options:
                options = SearchOptions()
            
            self.logger.info(f"开始图像搜索: {image_path}")
            
            # 检查缓存
            if self.config['enable_cache']:
                cached_result = self._get_cached_result(image_path, options)
                if cached_result:
                    self.logger.info(f"使用缓存结果，耗时: {time.time() - start_time:.2f}s")
                    return cached_result
            
            # 验证查询图像
            if not self._validate_image(image_path):
                return SearchResult(
                    query_image_path=image_path,
                    total_results=0,
                    search_time=time.time() - start_time,
                    results=[]
                )
            
            # 提取查询图像特征
            query_features = self._extract_query_features(image_path)
            if not query_features:
                self.logger.error(f"无法提取查询图像特征: {image_path}")
                return SearchResult(
                    query_image_path=image_path,
                    total_results=0,
                    search_time=time.time() - start_time,
                    results=[]
                )
            
            # 搜索相似图像
            matches = self._search_similar_images(query_features, options)
            
            # 创建搜索结果
            search_result = SearchResult(
                query_image_path=image_path,
                total_results=len(matches),
                search_time=time.time() - start_time,
                results=matches
            )
            
            # 缓存结果
            if self.config['enable_cache']:
                self._cache_result(image_path, options, search_result)
            
            # 记录搜索历史
            self._record_search_history(image_path, options, search_result)
            
            self.logger.info(f"图像搜索完成: 找到 {len(matches)} 个结果，耗时: {search_result.search_time:.2f}s")
            
            return search_result
            
        except Exception as e:
            self.logger.error(f"图像搜索失败: {e}")
            return SearchResult(
                query_image_path=image_path,
                total_results=0,
                search_time=time.time() - start_time,
                results=[]
            )
    
    def add_image_to_index(self, file_record: SharedFile) -> bool:
        """添加图像到搜索索引"""
        try:
            if not file_record.is_image:
                return False
            
            file_path = file_record.get_full_path()
            if not os.path.exists(file_path):
                self.logger.warning(f"文件不存在: {file_path}")
                return False
            
            if not self._validate_image(file_path):
                return False
            
            self.logger.debug(f"开始为图像建立索引: {file_record.filename}")
            
            # 检查是否已存在特征索引
            with self.db_manager.get_session() as session:
                existing_feature = session.query(ImageFeatureIndex).filter_by(
                    file_id=file_record.id
                ).first()
                
                if existing_feature:
                    self.logger.debug(f"图像特征已存在，跳过: {file_record.filename}")
                    return True
            
            # 提取图像特征
            features = self._extract_image_features(file_path, file_record.id)
            if not features:
                self.logger.error(f"特征提取失败: {file_record.filename}")
                return False
            
            # 保存特征到数据库
            success = self._save_image_features(features)
            if success:
                # 更新文件记录
                with self.db_manager.get_session() as session:
                    file_record.has_image_features = True
                    file_record.image_features_extracted_at = datetime.now()
                    session.merge(file_record)
                    session.commit()
                
                self.logger.debug(f"图像索引创建成功: {file_record.filename}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"添加图像索引失败: {e}")
            return False
    
    def remove_image_from_index(self, file_id: int) -> bool:
        """从搜索索引中移除图像"""
        try:
            with self.db_manager.get_session() as session:
                # 删除特征索引
                deleted_count = session.query(ImageFeatureIndex).filter_by(
                    file_id=file_id
                ).delete()
                
                # 更新文件记录
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if file_record:
                    file_record.has_image_features = False
                    file_record.image_features_extracted_at = None
                
                session.commit()
                
                if deleted_count > 0:
                    self.logger.debug(f"移除图像索引成功: file_id={file_id}")
                
                return True
                
        except Exception as e:
            self.logger.error(f"移除图像索引失败: {e}")
            return False
    
    def rebuild_index(self, folder_id: int = None) -> Dict[str, Any]:
        """重建图像搜索索引"""
        try:
            self.logger.info("开始重建图像搜索索引")
            start_time = time.time()
            
            with self.db_manager.get_session() as session:
                # 获取需要处理的图像文件
                query = session.query(SharedFile).filter_by(is_image=True)
                if folder_id:
                    query = query.filter_by(folder_id=folder_id)
                
                image_files = query.all()
                
                if not image_files:
                    return {
                        'success': True,
                        'message': '没有找到需要索引的图像文件',
                        'processed': 0,
                        'succeeded': 0,
                        'failed': 0,
                        'elapsed_time': time.time() - start_time
                    }
                
                # 清除现有索引（如果指定了文件夹）
                if folder_id:
                    file_ids = [f.id for f in image_files]
                    session.query(ImageFeatureIndex).filter(
                        ImageFeatureIndex.file_id.in_(file_ids)
                    ).delete(synchronize_session=False)
                    session.commit()
                
                # 批量处理图像
                processed = 0
                succeeded = 0
                failed = 0
                
                for file_record in image_files:
                    try:
                        if self.add_image_to_index(file_record):
                            succeeded += 1
                        else:
                            failed += 1
                        processed += 1
                        
                        # 每处理100个文件记录一次进度
                        if processed % 100 == 0:
                            self.logger.info(f"索引重建进度: {processed}/{len(image_files)}")
                            
                    except Exception as e:
                        self.logger.error(f"处理文件失败 {file_record.filename}: {e}")
                        failed += 1
                        processed += 1
                
                elapsed_time = time.time() - start_time
                
                self.logger.info(f"索引重建完成: 处理 {processed} 个文件, "
                               f"成功 {succeeded} 个, 失败 {failed} 个, "
                               f"耗时 {elapsed_time:.2f}s")
                
                return {
                    'success': True,
                    'processed': processed,
                    'succeeded': succeeded,
                    'failed': failed,
                    'elapsed_time': elapsed_time
                }
                
        except Exception as e:
            self.logger.error(f"重建索引失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'processed': 0,
                'succeeded': 0,
                'failed': 0,
                'elapsed_time': time.time() - start_time
            }
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        try:
            with self.db_manager.get_session() as session:
                # 索引统计
                total_indexed = session.query(ImageFeatureIndex).count()
                
                # 搜索历史统计
                total_searches = session.query(ImageSearchHistory).count()
                
                # 最近24小时的搜索
                yesterday = datetime.now() - timedelta(hours=24)
                recent_searches = session.query(ImageSearchHistory).filter(
                    ImageSearchHistory.created_at >= yesterday
                ).count()
                
                # 缓存统计
                total_cached = session.query(ImageSearchCache).count()
                expired_cached = session.query(ImageSearchCache).filter(
                    ImageSearchCache.expires_at < datetime.now()
                ).count()
                
                return {
                    'index_statistics': {
                        'total_indexed_images': total_indexed,
                        'feature_version': self.config['feature_version']
                    },
                    'search_statistics': {
                        'total_searches': total_searches,
                        'recent_searches_24h': recent_searches
                    },
                    'cache_statistics': {
                        'total_cached_results': total_cached,
                        'expired_cached_results': expired_cached,
                        'cache_enabled': self.config['enable_cache']
                    },
                    'configuration': {
                        'similarity_threshold': self.config['similarity_threshold'],
                        'max_results': self.config['max_results'],
                        'supported_formats': list(self.config['supported_formats']),
                        'partial_match_enabled': self.config['enable_partial_match']
                    }
                }
                
        except Exception as e:
            self.logger.error(f"获取搜索统计失败: {e}")
            return {}
    
    def _validate_image(self, image_path: str) -> bool:
        """验证图像文件"""
        try:
            if not os.path.exists(image_path):
                return False
            
            # 检查文件扩展名
            _, ext = os.path.splitext(image_path)
            if ext.lower() not in self.config['supported_formats']:
                return False
            
            # 检查文件大小（避免处理过大的文件）
            file_size = os.path.getsize(image_path)
            if file_size > 50 * 1024 * 1024:  # 50MB限制
                self.logger.warning(f"图像文件过大: {image_path} ({file_size} bytes)")
                return False
            
            # 尝试打开图像验证格式
            if HAS_PIL:
                try:
                    with Image.open(image_path) as img:
                        img.verify()
                    return True
                except Exception:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"图像验证失败: {e}")
            return False
    
    def _extract_query_features(self, image_path: str) -> Optional[ImageFeatures]:
        """提取查询图像的特征"""
        # 这里是占位符实现，实际的特征提取将在后续任务中实现
        try:
            features = ImageFeatures(file_id=0)  # 查询图像没有file_id
            features.extraction_time = datetime.now()
            features.feature_version = self.config['feature_version']
            
            # 生成图像哈希
            features.image_hash = self._generate_image_hash(image_path)
            
            return features
            
        except Exception as e:
            self.logger.error(f"提取查询图像特征失败: {e}")
            return None
    
    def _extract_image_features(self, image_path: str, file_id: int) -> Optional[ImageFeatures]:
        """提取图像特征"""
        # 这里是占位符实现，实际的特征提取将在后续任务中实现
        try:
            features = ImageFeatures(file_id=file_id)
            features.extraction_time = datetime.now()
            features.feature_version = self.config['feature_version']
            
            # 生成图像哈希
            features.image_hash = self._generate_image_hash(image_path)
            
            return features
            
        except Exception as e:
            self.logger.error(f"提取图像特征失败: {e}")
            return None
    
    def _generate_image_hash(self, image_path: str) -> str:
        """生成图像哈希"""
        try:
            # 简单的文件哈希实现
            with open(image_path, 'rb') as f:
                file_content = f.read()
                return hashlib.md5(file_content).hexdigest()
        except Exception as e:
            self.logger.error(f"生成图像哈希失败: {e}")
            return ""
    
    def _search_similar_images(self, query_features: ImageFeatures, options: SearchOptions) -> List[ImageMatch]:
        """搜索相似图像"""
        # 这里是占位符实现，实际的相似度计算将在后续任务中实现
        try:
            matches = []
            
            with self.db_manager.get_session() as session:
                # 获取所有已索引的图像
                indexed_features = session.query(ImageFeatureIndex).all()
                
                for feature_index in indexed_features:
                    # 简单的相似度计算（占位符）
                    similarity_score = 0.5  # 占位符分数
                    
                    if similarity_score >= options.similarity_threshold:
                        # 获取文件信息
                        file_record = session.query(SharedFile).filter_by(
                            id=feature_index.file_id
                        ).first()
                        
                        if file_record:
                            match = ImageMatch(
                                file_id=file_record.id,
                                filename=file_record.filename,
                                folder_id=file_record.folder_id,
                                similarity_score=similarity_score,
                                match_type='placeholder',
                                file_size=file_record.file_size
                            )
                            matches.append(match)
                
                # 按相似度排序
                matches.sort(key=lambda x: x.similarity_score, reverse=True)
                
                # 限制结果数量
                return matches[:options.max_results]
                
        except Exception as e:
            self.logger.error(f"搜索相似图像失败: {e}")
            return []
    
    def _save_image_features(self, features: ImageFeatures) -> bool:
        """保存图像特征到数据库"""
        try:
            with self.db_manager.get_session() as session:
                feature_index = ImageFeatureIndex(file_id=features.file_id)
                feature_index.set_features(features)
                
                session.add(feature_index)
                session.commit()
                
                return True
                
        except Exception as e:
            self.logger.error(f"保存图像特征失败: {e}")
            return False
    
    def _get_cached_result(self, image_path: str, options: SearchOptions) -> Optional[SearchResult]:
        """获取缓存的搜索结果"""
        try:
            query_hash = self._generate_query_hash(image_path)
            options_hash = self._generate_options_hash(options)
            
            with self.db_manager.get_session() as session:
                cached = session.query(ImageSearchCache).filter_by(
                    query_hash=query_hash,
                    options_hash=options_hash
                ).first()
                
                if cached and not cached.is_expired():
                    return cached.get_results()
                
                return None
                
        except Exception as e:
            self.logger.error(f"获取缓存结果失败: {e}")
            return None
    
    def _cache_result(self, image_path: str, options: SearchOptions, result: SearchResult):
        """缓存搜索结果"""
        try:
            query_hash = self._generate_query_hash(image_path)
            options_hash = self._generate_options_hash(options)
            expires_at = datetime.now() + timedelta(hours=self.config['cache_ttl_hours'])
            
            with self.db_manager.get_session() as session:
                # 删除现有缓存
                session.query(ImageSearchCache).filter_by(
                    query_hash=query_hash,
                    options_hash=options_hash
                ).delete()
                
                # 创建新缓存
                cache_entry = ImageSearchCache(
                    query_hash=query_hash,
                    options_hash=options_hash,
                    expires_at=expires_at
                )
                cache_entry.set_results(result)
                
                session.add(cache_entry)
                session.commit()
                
        except Exception as e:
            self.logger.error(f"缓存搜索结果失败: {e}")
    
    def _record_search_history(self, image_path: str, options: SearchOptions, result: SearchResult):
        """记录搜索历史"""
        try:
            with self.db_manager.get_session() as session:
                history = ImageSearchHistory(
                    query_image_hash=self._generate_query_hash(image_path),
                    query_image_path=image_path,
                    result_count=result.total_results,
                    search_time=result.search_time
                )
                history.set_search_options(options)
                
                session.add(history)
                session.commit()
                
        except Exception as e:
            self.logger.error(f"记录搜索历史失败: {e}")
    
    def _generate_query_hash(self, image_path: str) -> str:
        """生成查询哈希"""
        try:
            # 使用文件内容生成哈希
            with open(image_path, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()
        except Exception as e:
            self.logger.error(f"生成查询哈希失败: {e}")
            return hashlib.md5(image_path.encode()).hexdigest()
    
    def _generate_options_hash(self, options: SearchOptions) -> str:
        """生成选项哈希"""
        try:
            options_str = f"{options.similarity_threshold}_{options.max_results}_{options.enable_partial_match}"
            options_str += f"_{options.color_weight}_{options.texture_weight}_{options.keypoint_weight}"
            
            if options.folder_filter:
                options_str += f"_folders_{'_'.join(map(str, options.folder_filter))}"
            
            if options.file_type_filter:
                options_str += f"_types_{'_'.join(options.file_type_filter)}"
            
            return hashlib.md5(options_str.encode()).hexdigest()
            
        except Exception as e:
            self.logger.error(f"生成选项哈希失败: {e}")
            return "default"