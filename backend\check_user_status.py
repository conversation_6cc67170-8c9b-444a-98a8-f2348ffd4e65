#!/usr/bin/env python3
"""
检查用户状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import DatabaseManager
from models.user import User

def check_user_status():
    """检查用户状态"""
    
    # 初始化数据库
    db_manager = DatabaseManager()
    
    try:
        with db_manager.get_session() as session:
            # 查找测试用户
            user = session.query(User).filter_by(username='fii').first()
            
            if not user:
                print("用户 'fii' 不存在")
                return
                
            print(f"用户ID: {user.id}")
            print(f"用户名: {user.username}")
            print(f"是否被禁用: {user.is_banned}")
            print(f"禁用到期时间: {user.ban_until}")
            print(f"当前是否被禁用: {user.is_banned_now()}")
            print(f"失败登录次数: {user.failed_login_attempts}")
            
            # 清除旧的封禁状态
            if user.is_banned:
                print("清除旧的封禁状态...")
                user.unban_user()
                session.commit()
                print("旧封禁状态已清除")
                
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_user_status()
