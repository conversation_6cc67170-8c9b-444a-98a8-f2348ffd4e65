#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理服务
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import mimetypes

from models.file_share import SharedFolder, SharedFile, SharedDirectory
from services.directory_service import DirectoryService
from utils.logger import setup_logger

class FileService:
    """文件管理服务类"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("FileService")
        self.directory_service = DirectoryService(db_manager)

    def scan_shared_folder_with_directories(self, folder_id: int) -> Dict[str, Any]:
        """扫描共享文件夹，创建目录结构（新版本）"""
        return self.directory_service.scan_folder_with_directories(folder_id)

    def scan_shared_folder(self, folder_id: int, generate_thumbnails: bool = True) -> Dict[str, Any]:
        """扫描共享文件夹，更新文件信息并预生成缩略图"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}

                if not folder.is_path_valid():
                    return {"success": False, "error": "文件夹路径无效"}

                # 获取缩略图服务
                thumbnail_service = None
                if generate_thumbnails:
                    try:
                        from services.thumbnail_service import ThumbnailService
                        thumbnail_service = ThumbnailService()
                        self.logger.info("缩略图服务已启用，将在扫描时预生成缩略图")
                    except Exception as e:
                        self.logger.warning(f"缩略图服务初始化失败: {e}")

                # 获取图像搜索服务
                image_search_service = None
                try:
                    from services.image_search_service import ImageSearchService
                    image_search_service = ImageSearchService(self.db_manager)
                    self.logger.info("图像搜索服务已启用，将在扫描时提取图像特征")
                except Exception as e:
                    self.logger.warning(f"图像搜索服务初始化失败: {e}")

                # 扫描文件
                scanned_files = []
                total_size = 0
                thumbnail_generated = 0
                thumbnail_failed = 0
                features_extracted = 0
                features_failed = 0

                for root, dirs, files in os.walk(folder.path):
                    for filename in files:
                        file_path = os.path.join(root, filename)
                        relative_path = os.path.relpath(file_path, folder.path)

                        try:
                            # 获取文件信息
                            stat = os.stat(file_path)
                            file_size = stat.st_size
                            file_modified = datetime.fromtimestamp(stat.st_mtime)

                            # 检查文件是否已存在
                            existing_file = session.query(SharedFile).filter_by(
                                folder_id=folder_id,
                                relative_path=relative_path
                            ).first()

                            file_record = None
                            if existing_file:
                                # 更新现有文件信息
                                existing_file.file_size = file_size
                                existing_file.file_modified = file_modified
                                existing_file.update_file_info()
                                file_record = existing_file
                            else:
                                # 创建新文件记录
                                new_file = SharedFile(
                                    folder_id=folder_id,
                                    filename=filename,
                                    relative_path=relative_path,
                                    file_size=file_size,
                                    file_modified=file_modified
                                )
                                new_file.update_file_info()
                                session.add(new_file)
                                session.flush()  # 获取文件ID
                                file_record = new_file

                            # 预生成缩略图（仅对图像文件）
                            if thumbnail_service and file_record and file_record.is_image:
                                try:
                                    # 检查缩略图是否已存在且是最新的
                                    thumbnail_path = thumbnail_service.get_thumbnail_path(file_path, 'medium')
                                    needs_regenerate = True

                                    if thumbnail_path and os.path.exists(thumbnail_path):
                                        # 检查缩略图是否比原文件新
                                        thumb_mtime = os.path.getmtime(thumbnail_path)
                                        file_mtime = os.path.getmtime(file_path)
                                        if thumb_mtime >= file_mtime:
                                            needs_regenerate = False

                                    if needs_regenerate:
                                        # 生成缩略图（medium 和 large 尺寸）
                                        medium_thumb = thumbnail_service.generate_thumbnail(file_path, 'medium')
                                        large_thumb = thumbnail_service.generate_thumbnail(file_path, 'large')

                                        if medium_thumb or large_thumb:
                                            thumbnail_generated += 1
                                            self.logger.debug(f"缩略图生成成功: {filename}")
                                        else:
                                            thumbnail_failed += 1
                                            self.logger.debug(f"缩略图生成失败: {filename}")

                                except Exception as e:
                                    thumbnail_failed += 1
                                    self.logger.debug(f"缩略图生成异常 {filename}: {e}")

                            # 提取图像特征（仅对图像文件）
                            if image_search_service and file_record and file_record.is_image:
                                try:
                                    # 检查是否已有特征索引
                                    if not file_record.has_image_features:
                                        if image_search_service.add_image_to_index(file_record):
                                            features_extracted += 1
                                            self.logger.debug(f"图像特征提取成功: {filename}")
                                        else:
                                            features_failed += 1
                                            self.logger.debug(f"图像特征提取失败: {filename}")
                                    else:
                                        self.logger.debug(f"图像特征已存在，跳过: {filename}")

                                except Exception as e:
                                    features_failed += 1
                                    self.logger.debug(f"图像特征提取异常 {filename}: {e}")

                            scanned_files.append(relative_path)
                            total_size += file_size

                        except Exception as e:
                            self.logger.warning(f"处理文件失败 {file_path}: {e}")
                            continue
                
                # 删除不存在的文件记录
                existing_files = session.query(SharedFile).filter_by(folder_id=folder_id).all()
                for file_record in existing_files:
                    if file_record.relative_path not in scanned_files:
                        session.delete(file_record)

                # 更新文件夹统计信息
                folder.file_count = len(scanned_files)
                folder.total_size = total_size
                folder.last_scanned = datetime.now()

                session.commit()

                # 记录扫描结果
                log_msg = f"文件夹扫描完成: {folder.name}, 文件数: {len(scanned_files)}"
                if thumbnail_service:
                    log_msg += f", 缩略图生成: {thumbnail_generated}, 失败: {thumbnail_failed}"
                if image_search_service:
                    log_msg += f", 特征提取: {features_extracted}, 失败: {features_failed}"
                
                self.logger.info(log_msg)

                result = {
                    "success": True,
                    "files_count": len(scanned_files),
                    "total_size": total_size,
                    "folder_name": folder.name
                }

                # 添加缩略图统计信息
                if thumbnail_service:
                    result.update({
                        "thumbnail_generated": thumbnail_generated,
                        "thumbnail_failed": thumbnail_failed,
                        "thumbnail_total": thumbnail_generated + thumbnail_failed
                    })

                # 添加图像特征统计信息
                if image_search_service:
                    result.update({
                        "features_extracted": features_extracted,
                        "features_failed": features_failed,
                        "features_total": features_extracted + features_failed
                    })

                return result
                
        except Exception as e:
            self.logger.error(f"扫描文件夹失败: {e}")
            return {"success": False, "error": str(e)}

    def generate_thumbnails_for_folder(self, folder_id: int, force_regenerate: bool = False) -> Dict[str, Any]:
        """为指定文件夹的所有图像文件生成缩略图"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}

                # 获取缩略图服务
                try:
                    from services.thumbnail_service import ThumbnailService
                    thumbnail_service = ThumbnailService()
                except Exception as e:
                    return {"success": False, "error": f"缩略图服务初始化失败: {e}"}

                # 获取所有图像文件
                image_files = session.query(SharedFile).filter_by(
                    folder_id=folder_id,
                    is_image=True
                ).all()

                if not image_files:
                    return {
                        "success": True,
                        "message": "该文件夹中没有图像文件",
                        "processed": 0,
                        "generated": 0,
                        "failed": 0
                    }

                processed = 0
                generated = 0
                failed = 0

                for file_record in image_files:
                    try:
                        file_path = file_record.get_full_path()
                        if not os.path.exists(file_path):
                            self.logger.warning(f"文件不存在: {file_path}")
                            failed += 1
                            continue

                        # 检查是否需要生成缩略图
                        needs_generate = force_regenerate
                        if not needs_generate:
                            thumbnail_path = thumbnail_service.get_thumbnail_path(file_path, 'medium')
                            if not thumbnail_path or not os.path.exists(thumbnail_path):
                                needs_generate = True
                            else:
                                # 检查缩略图是否比原文件新
                                thumb_mtime = os.path.getmtime(thumbnail_path)
                                file_mtime = os.path.getmtime(file_path)
                                if thumb_mtime < file_mtime:
                                    needs_generate = True

                        if needs_generate:
                            # 生成缩略图
                            medium_thumb = thumbnail_service.generate_thumbnail(file_path, 'medium')
                            large_thumb = thumbnail_service.generate_thumbnail(file_path, 'large')

                            if medium_thumb or large_thumb:
                                generated += 1
                                self.logger.debug(f"缩略图生成成功: {file_record.filename}")
                            else:
                                failed += 1
                                self.logger.debug(f"缩略图生成失败: {file_record.filename}")

                        processed += 1

                    except Exception as e:
                        failed += 1
                        self.logger.warning(f"处理文件失败 {file_record.filename}: {e}")

                self.logger.info(f"文件夹 {folder.name} 缩略图生成完成: "
                               f"处理 {processed} 个文件, 生成 {generated} 个, 失败 {failed} 个")

                return {
                    "success": True,
                    "folder_name": folder.name,
                    "processed": processed,
                    "generated": generated,
                    "failed": failed,
                    "total_images": len(image_files)
                }

        except Exception as e:
            self.logger.error(f"批量生成缩略图失败: {e}")
            return {"success": False, "error": str(e)}

    def generate_thumbnails_for_all_folders(self, force_regenerate: bool = False) -> Dict[str, Any]:
        """为所有文件夹的图像文件生成缩略图"""
        try:
            with self.db_manager.get_session() as session:
                folders = session.query(SharedFolder).all()

                if not folders:
                    return {
                        "success": True,
                        "message": "没有找到共享文件夹",
                        "folders_processed": 0,
                        "total_generated": 0,
                        "total_failed": 0
                    }

                folders_processed = 0
                total_generated = 0
                total_failed = 0
                results = []

                for folder in folders:
                    try:
                        self.logger.info(f"开始为文件夹 {folder.name} 生成缩略图...")
                        result = self.generate_thumbnails_for_folder(folder.id, force_regenerate)

                        if result["success"]:
                            folders_processed += 1
                            total_generated += result.get("generated", 0)
                            total_failed += result.get("failed", 0)

                            results.append({
                                "folder_name": folder.name,
                                "folder_id": folder.id,
                                "generated": result.get("generated", 0),
                                "failed": result.get("failed", 0),
                                "total_images": result.get("total_images", 0)
                            })
                        else:
                            self.logger.error(f"文件夹 {folder.name} 缩略图生成失败: {result.get('error')}")
                            results.append({
                                "folder_name": folder.name,
                                "folder_id": folder.id,
                                "error": result.get("error")
                            })

                    except Exception as e:
                        self.logger.error(f"处理文件夹 {folder.name} 时出错: {e}")
                        results.append({
                            "folder_name": folder.name,
                            "folder_id": folder.id,
                            "error": str(e)
                        })

                self.logger.info(f"所有文件夹缩略图生成完成: "
                               f"处理 {folders_processed} 个文件夹, 生成 {total_generated} 个缩略图, 失败 {total_failed} 个")

                return {
                    "success": True,
                    "folders_processed": folders_processed,
                    "total_folders": len(folders),
                    "total_generated": total_generated,
                    "total_failed": total_failed,
                    "results": results
                }

        except Exception as e:
            self.logger.error(f"批量生成所有缩略图失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_file_info(self, file_id: int) -> Optional[Dict[str, Any]]:
        """获取文件详细信息"""
        try:
            with self.db_manager.get_session() as session:
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if not file_record:
                    return None
                
                file_info = file_record.to_dict()

                # 添加实时文件信息
                full_path = file_record.get_full_path()
                file_info['full_path'] = full_path  # 添加完整路径

                if os.path.exists(full_path):
                    stat = os.stat(full_path)
                    file_info['current_size'] = stat.st_size
                    file_info['current_modified'] = datetime.fromtimestamp(stat.st_mtime).isoformat()
                    file_info['exists'] = True
                else:
                    file_info['exists'] = False

                return file_info
                
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return None
    
    def get_folder_files(self, folder_id: int, page: int = 1,
                        page_size: int = 50, search_query: str = None) -> Dict[str, Any]:
        """获取文件夹中的文件列表"""
        try:
            with self.db_manager.get_session() as session:
                # 首先检查文件夹的读取权限
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}

                # 检查读取权限
                if not folder.allow_read:
                    return {"success": False, "error": "没有访问权限"}

                query = session.query(SharedFile).filter_by(folder_id=folder_id)

                # 搜索过滤
                if search_query:
                    query = query.filter(SharedFile.filename.contains(search_query))

                # 分页
                total_count = query.count()
                offset = (page - 1) * page_size
                files = query.offset(offset).limit(page_size).all()

                files_data = [file.to_dict() for file in files]

                return {
                    "success": True,
                    "files": files_data,
                    "total_count": total_count,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total_count + page_size - 1) // page_size
                }
                
        except Exception as e:
            self.logger.error(f"获取文件列表失败: {e}")
            return {"success": False, "error": str(e)}

    def get_directory_contents(self, folder_id: int, directory_id: int = None,
                              page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """获取目录内容（支持层级结构）"""
        return self.directory_service.get_directory_contents(folder_id, directory_id, page, page_size)

    def get_directory_breadcrumb(self, folder_id: int, directory_id: int = None) -> List[Dict[str, Any]]:
        """获取目录面包屑导航"""
        return self.directory_service.get_directory_breadcrumb(folder_id, directory_id)

    def create_shared_folder(self, name: str, path: str, **kwargs) -> Dict[str, Any]:
        """创建共享文件夹"""
        try:
            # 验证路径
            if not os.path.exists(path) or not os.path.isdir(path):
                return {"success": False, "error": "路径不存在或不是文件夹"}
            
            with self.db_manager.get_session() as session:
                # 检查是否已存在相同路径
                existing = session.query(SharedFolder).filter_by(path=path).first()
                if existing:
                    return {"success": False, "error": "该路径已被共享"}
                
                # 创建共享文件夹
                folder = SharedFolder(name=name, path=path, **kwargs)
                session.add(folder)
                session.commit()
                
                # 扫描文件夹
                scan_result = self.scan_shared_folder(folder.id)
                
                self.logger.info(f"创建共享文件夹成功: {name} -> {path}")
                
                return {
                    "success": True,
                    "folder_id": folder.id,
                    "scan_result": scan_result
                }
                
        except Exception as e:
            self.logger.error(f"创建共享文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def update_shared_folder(self, folder_id: int, updates: Dict[str, Any]) -> Dict[str, Any]:
        """更新共享文件夹设置"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                # 更新属性
                for key, value in updates.items():
                    if hasattr(folder, key):
                        setattr(folder, key, value)
                
                session.commit()
                
                self.logger.info(f"更新共享文件夹成功: {folder.name}")
                
                return {"success": True}
                
        except Exception as e:
            self.logger.error(f"更新共享文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def delete_shared_folder(self, folder_id: int) -> Dict[str, Any]:
        """删除共享文件夹"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}

                folder_name = folder.name

                # 获取该文件夹下的所有文件ID
                file_ids = session.query(SharedFile.id).filter_by(folder_id=folder_id).all()
                file_ids = [file_id[0] for file_id in file_ids]

                self.logger.info(f"准备删除文件夹 '{folder_name}'，包含 {len(file_ids)} 个文件")

                # 手动删除相关的统计数据，避免外键约束问题
                if file_ids:
                    # 删除下载统计记录
                    from models.download_record import DownloadStatistics
                    deleted_stats = session.query(DownloadStatistics).filter(
                        DownloadStatistics.file_id.in_(file_ids)
                    ).delete(synchronize_session=False)
                    self.logger.info(f"删除了 {deleted_stats} 条下载统计记录")

                    # 删除密码申请记录
                    from models.download_record import PasswordRequest
                    deleted_requests = session.query(PasswordRequest).filter(
                        PasswordRequest.file_id.in_(file_ids)
                    ).delete(synchronize_session=False)
                    self.logger.info(f"删除了 {deleted_requests} 条密码申请记录")

                    # 删除搜索索引记录
                    from models.search_index import SearchIndex
                    deleted_indexes = session.query(SearchIndex).filter(
                        SearchIndex.file_id.in_(file_ids)
                    ).delete(synchronize_session=False)
                    self.logger.info(f"删除了 {deleted_indexes} 条搜索索引记录")

                # 现在删除文件夹（会自动级联删除文件记录）
                session.delete(folder)
                session.commit()

                self.logger.info(f"删除共享文件夹成功: {folder_name}")

                return {"success": True}

        except Exception as e:
            self.logger.error(f"删除共享文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_shared_folders(self, admin_view: bool = False) -> Dict[str, Any]:
        """获取共享文件夹列表

        Args:
            admin_view: 如果为True，返回所有文件夹（管理员视图）；否则只返回有读取权限的文件夹
        """
        try:
            with self.db_manager.get_session() as session:
                if admin_view:
                    # 管理员视图：获取所有文件夹，不考虑权限
                    folders = session.query(SharedFolder).all()
                else:
                    # 普通用户视图：只获取有读取权限的文件夹
                    folders = session.query(SharedFolder).filter_by(allow_read=True).all()

                folder_list = []
                for folder in folders:
                    # 统计文件数量
                    file_count = session.query(SharedFile).filter(
                        SharedFile.folder_id == folder.id
                    ).count()

                    folder_dict = folder.to_dict()
                    folder_dict['file_count'] = file_count
                    folder_list.append(folder_dict)

                return {
                    'success': True,
                    'folders': folder_list
                }

        except Exception as e:
            self.logger.error(f"获取共享文件夹列表失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.error(f"计算文件哈希失败: {e}")
            return ""
    
    def get_file_mime_type(self, file_path: str) -> str:
        """获取文件MIME类型"""
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or "application/octet-stream"
    
    def is_file_allowed(self, filename: str, folder_id: int) -> bool:
        """检查文件是否被允许"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder or not folder.allowed_extensions:
                    return True

                _, ext = os.path.splitext(filename)
                return ext.lower() in folder.allowed_extensions

        except Exception as e:
            self.logger.error(f"检查文件权限失败: {e}")
            return False

    def check_folder_permission(self, folder_id: int, permission: str) -> bool:
        """检查文件夹权限"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return False

                if permission == 'read':
                    return folder.allow_read
                elif permission == 'write':
                    return folder.allow_write
                elif permission == 'delete':
                    return folder.allow_delete
                elif permission == 'upload':
                    return folder.allow_upload
                elif permission == 'download':
                    return folder.allow_download
                else:
                    return False

        except Exception as e:
            self.logger.error(f"检查文件夹权限失败: {e}")
            return False
    
    def get_folder_statistics(self, folder_id: int) -> Dict[str, Any]:
        """获取文件夹统计信息"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                # 文件类型统计
                files = session.query(SharedFile).filter_by(folder_id=folder_id).all()
                
                stats = {
                    "total_files": len(files),
                    "total_size": folder.total_size,
                    "file_types": {},
                    "size_distribution": {
                        "small": 0,    # < 1MB
                        "medium": 0,   # 1MB - 10MB
                        "large": 0,    # 10MB - 100MB
                        "xlarge": 0    # > 100MB
                    }
                }
                
                for file in files:
                    # 文件类型统计
                    ext = file.extension or "unknown"
                    stats["file_types"][ext] = stats["file_types"].get(ext, 0) + 1
                    
                    # 大小分布统计
                    size_mb = file.file_size / (1024 * 1024)
                    if size_mb < 1:
                        stats["size_distribution"]["small"] += 1
                    elif size_mb < 10:
                        stats["size_distribution"]["medium"] += 1
                    elif size_mb < 100:
                        stats["size_distribution"]["large"] += 1
                    else:
                        stats["size_distribution"]["xlarge"] += 1
                
                return {"success": True, "statistics": stats}

        except Exception as e:
            self.logger.error(f"获取文件夹统计失败: {e}")
            return {"success": False, "error": str(e)}



    def delete_file_record(self, file_id: int) -> Dict[str, Any]:
        """删除文件记录（不删除实际文件）"""
        try:
            with self.db_manager.get_session() as session:
                file_record = session.query(SharedFile).filter_by(id=file_id).first()

                if not file_record:
                    return {
                        'success': False,
                        'error': '文件记录不存在'
                    }

                file_name = file_record.filename
                session.delete(file_record)
                session.commit()

                self.logger.info(f"删除文件记录成功: {file_name}")

                return {
                    'success': True,
                    'message': '文件记录删除成功'
                }

        except Exception as e:
            self.logger.error(f"删除文件记录失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def scan_folder(self, folder_id: int) -> Dict[str, Any]:
        """扫描指定文件夹"""
        try:
            return self.scan_shared_folder(folder_id)
        except Exception as e:
            self.logger.error(f"扫描文件夹失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def set_folder_permissions(self, folder_id: int, permissions: Dict[str, Any]) -> Dict[str, Any]:
        """设置文件夹权限"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}

                # 更新网络访问权限
                network_access = permissions.get('network_access', {})
                folder.allow_internal = network_access.get('internal', True)
                folder.allow_external = network_access.get('external', False)

                # 更新文件操作权限
                file_operations = permissions.get('file_operations', {})
                folder.allow_read = file_operations.get('read', True)
                folder.allow_write = file_operations.get('write', False)
                folder.allow_delete = file_operations.get('delete', False)
                folder.allow_upload = file_operations.get('write', False)  # 写入权限包含上传
                folder.allow_download = file_operations.get('download', True)  # 独立的下载权限
                folder.show_details = file_operations.get('show_details', True)

                # 更新下载限制
                download_limits = permissions.get('download_limits', {})
                max_size_mb = download_limits.get('max_file_size_mb', 100)
                folder.max_file_size = max_size_mb * 1024 * 1024  # 转换为字节
                folder.max_batch_files = download_limits.get('max_batch_files', 10)

                # 更新加密设置
                encryption = permissions.get('encryption', {})
                folder.encrypt_after_downloads = encryption.get('encrypt_after_downloads', 5)

                session.commit()

                self.logger.info(f"设置文件夹权限成功: {folder.name}")

                return {"success": True, "message": "权限设置已保存"}

        except Exception as e:
            self.logger.error(f"设置文件夹权限失败: {e}")
            return {"success": False, "error": str(e)}

    def get_folder_permissions(self, folder_id: int) -> Dict[str, Any]:
        """获取文件夹权限"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}

                # 从现有字段构建权限设置
                permissions = {
                    'network_access': {
                        'internal': folder.allow_internal,
                        'external': folder.allow_external
                    },
                    'file_operations': {
                        'read': folder.allow_read,
                        'download': folder.allow_download,
                        'write': folder.allow_write,
                        'delete': folder.allow_delete,
                        'replace': folder.allow_write,  # 替换权限等同于写入权限
                        'show_details': folder.show_details
                    },
                    'download_limits': {
                        'max_file_size_mb': int(folder.max_file_size / (1024 * 1024)) if folder.max_file_size > 0 else 100,
                        'max_batch_files': folder.max_batch_files or 10
                    },
                    'encryption': {
                        'encrypt_after_downloads': folder.encrypt_after_downloads or 5
                    }
                }

                return {"success": True, "permissions": permissions}

        except Exception as e:
            self.logger.error(f"获取文件夹权限失败: {e}")
            return {"success": False, "error": str(e)}

    def get_root_files(self, page: int = 1, page_size: int = 50, search_query: str = None) -> Dict[str, Any]:
        """获取根目录文件列表（所有共享文件夹的文件）"""
        try:
            with self.db_manager.get_session() as session:
                # 获取所有有读取权限的共享文件夹
                folders = session.query(SharedFolder).filter_by(allow_read=True).all()

                all_files = []

                # 遍历每个文件夹，获取文件
                for folder in folders:
                    query = session.query(SharedFile).filter_by(folder_id=folder.id)

                    # 搜索过滤
                    if search_query:
                        query = query.filter(SharedFile.filename.contains(search_query))

                    files = query.all()

                    # 添加文件夹信息到文件数据中
                    for file in files:
                        file_dict = file.to_dict()
                        file_dict['folder_name'] = folder.name
                        file_dict['folder_path'] = folder.path
                        all_files.append(file_dict)

                # 添加有读取权限的文件夹作为可导航的项目
                for folder in folders:
                    folder_dict = {
                        'id': folder.id,
                        'name': folder.name,
                        'type': 'folder',
                        'size': 0,
                        'modified_at': folder.created_at.isoformat() if folder.created_at else None,
                        'file_count': session.query(SharedFile).filter_by(folder_id=folder.id).count()
                    }
                    all_files.append(folder_dict)

                # 分页处理
                total_count = len(all_files)
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                paginated_files = all_files[start_idx:end_idx]

                return {
                    "success": True,
                    "files": paginated_files,
                    "total_count": total_count,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total_count + page_size - 1) // page_size
                }

        except Exception as e:
            self.logger.error(f"获取根目录文件列表失败: {e}")
            return {"success": False, "error": str(e)}
