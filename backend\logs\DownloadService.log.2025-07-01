2025-07-01 07:56:59 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 08:27:01 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 09:17:07 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 10:35:37 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 10:35:45 - DownloadService - INFO - 准备文件夹下载: folder_id=8, user_id=1, request_context={'session_id': None, 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'download_source': 'web'}
2025-07-01 10:36:02 - DownloadService - INFO - 创建下载批次成功: 17c5ac24-69fc-468f-84d3-31b36c7434bd, 类型=folder, 目标=folder:8
2025-07-01 10:36:02 - DownloadService - INFO - 批次状态更新成功: 17c5ac24-69fc-468f-84d3-31b36c7434bd -> ready
2025-07-01 10:36:02 - DownloadService - INFO - 记录下载成功: 文件夹ID=8, 用户ID=1, 类型=folder, 批次=17c5ac24-69fc-468f-84d3-31b36c7434bd, 加密=False
2025-07-01 11:09:01 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 11:16:50 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 11:20:46 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 11:21:45 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 11:25:28 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 11:26:33 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 15:24:23 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 15:24:55 - DownloadService - INFO - 创建下载批次成功: f8b742bc-86e7-410d-b8c6-2d79156b4631, 类型=single, 目标=file:704
2025-07-01 15:24:55 - DownloadService - INFO - 批次状态更新成功: f8b742bc-86e7-410d-b8c6-2d79156b4631 -> ready
2025-07-01 15:24:55 - DownloadService - INFO - 记录下载成功: 文件ID=704, 用户ID=1, 类型=single, 批次=f8b742bc-86e7-410d-b8c6-2d79156b4631, 加密=False
2025-07-01 15:24:55 - DownloadService - INFO - 记录下载成功: 文件ID=704, 用户ID=1, 类型=single, 批次=f8b742bc-86e7-410d-b8c6-2d79156b4631, 加密=False
2025-07-01 15:24:55 - DownloadService - INFO - 创建下载批次成功: 494f9085-6306-4c34-b7b0-93cc98096c4e, 类型=single, 目标=file:704
2025-07-01 15:24:55 - DownloadService - INFO - 批次状态更新成功: 494f9085-6306-4c34-b7b0-93cc98096c4e -> ready
2025-07-01 15:24:55 - DownloadService - INFO - 记录下载成功: 文件ID=704, 用户ID=1, 类型=single, 批次=494f9085-6306-4c34-b7b0-93cc98096c4e, 加密=False
2025-07-01 15:24:55 - DownloadService - INFO - 记录下载成功: 文件ID=704, 用户ID=1, 类型=single, 批次=494f9085-6306-4c34-b7b0-93cc98096c4e, 加密=False
2025-07-01 15:25:13 - DownloadService - INFO - 创建下载批次成功: c23de39e-6f89-415a-bf3c-e3d8e8fa9eed, 类型=single, 目标=file:706
2025-07-01 15:25:13 - DownloadService - INFO - 批次状态更新成功: c23de39e-6f89-415a-bf3c-e3d8e8fa9eed -> ready
2025-07-01 15:25:13 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=c23de39e-6f89-415a-bf3c-e3d8e8fa9eed, 加密=False
2025-07-01 15:25:13 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=c23de39e-6f89-415a-bf3c-e3d8e8fa9eed, 加密=False
2025-07-01 15:25:14 - DownloadService - INFO - 创建下载批次成功: 3ed510de-7f40-4077-bceb-ae3ef51af5ec, 类型=single, 目标=file:706
2025-07-01 15:25:14 - DownloadService - INFO - 批次状态更新成功: 3ed510de-7f40-4077-bceb-ae3ef51af5ec -> ready
2025-07-01 15:25:14 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=3ed510de-7f40-4077-bceb-ae3ef51af5ec, 加密=False
2025-07-01 15:25:14 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=3ed510de-7f40-4077-bceb-ae3ef51af5ec, 加密=False
2025-07-01 15:25:35 - DownloadService - INFO - 批量下载包含 1 个需要加密的文件
2025-07-01 15:25:35 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-07-01 15:25:36 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-07-01 15:25:37 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\batch_download_20250701_152535.zip
2025-07-01 15:25:37 - DownloadService - INFO - 创建下载批次成功: fdec7a44-627e-4148-ac6e-94cad269fc31, 类型=batch, 目标=file:None
2025-07-01 15:25:37 - DownloadService - INFO - 批次状态更新成功: fdec7a44-627e-4148-ac6e-94cad269fc31 -> ready
2025-07-01 15:25:37 - DownloadService - INFO - 记录下载成功: 文件夹ID=None, 用户ID=1, 类型=batch, 批次=fdec7a44-627e-4148-ac6e-94cad269fc31, 加密=True
2025-07-01 15:25:37 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=706, 用户ID=1
2025-07-01 15:36:00 - DownloadService - INFO - 文件 0ad4b2c9b604d426 [转换].jpg 需要加密下载
2025-07-01 15:36:00 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-07-01 15:36:00 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-07-01 15:36:00 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\0ad4b2c9b604d426 [转换].jpg_20250701_153600.zip
2025-07-01 15:36:00 - DownloadService - INFO - 创建下载批次成功: f2c29c47-0b74-4962-a9da-1eb299a79ad9, 类型=single, 目标=file:704
2025-07-01 15:36:00 - DownloadService - INFO - 批次状态更新成功: f2c29c47-0b74-4962-a9da-1eb299a79ad9 -> ready
2025-07-01 15:36:00 - DownloadService - INFO - 记录下载成功: 文件ID=704, 用户ID=1, 类型=single, 批次=f2c29c47-0b74-4962-a9da-1eb299a79ad9, 加密=True
2025-07-01 15:36:00 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=704, 用户ID=1
2025-07-01 15:36:00 - DownloadService - INFO - 记录下载成功: 文件ID=704, 用户ID=1, 类型=single, 批次=f2c29c47-0b74-4962-a9da-1eb299a79ad9, 加密=True
2025-07-01 15:36:00 - DownloadService - INFO - 文件 0ad4b2c9b604d426 [转换].jpg 需要加密下载
2025-07-01 15:36:00 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-07-01 15:36:00 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-07-01 15:36:00 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\0ad4b2c9b604d426 [转换].jpg_20250701_153600.zip
2025-07-01 15:36:00 - DownloadService - INFO - 创建下载批次成功: 26795208-3312-48ae-88d4-a21ad4147627, 类型=single, 目标=file:704
2025-07-01 15:36:00 - DownloadService - INFO - 批次状态更新成功: 26795208-3312-48ae-88d4-a21ad4147627 -> ready
2025-07-01 15:36:00 - DownloadService - INFO - 记录下载成功: 文件ID=704, 用户ID=1, 类型=single, 批次=26795208-3312-48ae-88d4-a21ad4147627, 加密=True
2025-07-01 15:36:00 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=704, 用户ID=1
2025-07-01 15:36:00 - DownloadService - INFO - 记录下载成功: 文件ID=704, 用户ID=1, 类型=single, 批次=26795208-3312-48ae-88d4-a21ad4147627, 加密=True
2025-07-01 15:36:14 - DownloadService - INFO - 创建下载批次成功: 0b1f40ce-bdfd-4664-8a5a-e0af5e6263e9, 类型=single, 目标=file:705
2025-07-01 15:36:14 - DownloadService - INFO - 批次状态更新成功: 0b1f40ce-bdfd-4664-8a5a-e0af5e6263e9 -> ready
2025-07-01 15:36:14 - DownloadService - INFO - 记录下载成功: 文件ID=705, 用户ID=1, 类型=single, 批次=0b1f40ce-bdfd-4664-8a5a-e0af5e6263e9, 加密=False
2025-07-01 15:36:14 - DownloadService - INFO - 记录下载成功: 文件ID=705, 用户ID=1, 类型=single, 批次=0b1f40ce-bdfd-4664-8a5a-e0af5e6263e9, 加密=False
2025-07-01 15:36:18 - DownloadService - INFO - 创建下载批次成功: 8e360b41-6ed9-4263-9081-24ed5b533519, 类型=single, 目标=file:705
2025-07-01 15:36:18 - DownloadService - INFO - 批次状态更新成功: 8e360b41-6ed9-4263-9081-24ed5b533519 -> ready
2025-07-01 15:36:18 - DownloadService - INFO - 记录下载成功: 文件ID=705, 用户ID=1, 类型=single, 批次=8e360b41-6ed9-4263-9081-24ed5b533519, 加密=False
2025-07-01 15:36:18 - DownloadService - INFO - 记录下载成功: 文件ID=705, 用户ID=1, 类型=single, 批次=8e360b41-6ed9-4263-9081-24ed5b533519, 加密=False
2025-07-01 15:36:18 - DownloadService - INFO - 文件 128270.eps 需要加密下载
2025-07-01 15:36:18 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-07-01 15:36:20 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-07-01 15:36:21 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\128270.eps_20250701_153618.zip
2025-07-01 15:36:21 - DownloadService - INFO - 创建下载批次成功: 2082a02d-fa2d-4e07-b46c-c673dd3e500e, 类型=single, 目标=file:706
2025-07-01 15:36:21 - DownloadService - INFO - 批次状态更新成功: 2082a02d-fa2d-4e07-b46c-c673dd3e500e -> ready
2025-07-01 15:36:21 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=2082a02d-fa2d-4e07-b46c-c673dd3e500e, 加密=True
2025-07-01 15:36:21 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=706, 用户ID=1
2025-07-01 15:36:21 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=2082a02d-fa2d-4e07-b46c-c673dd3e500e, 加密=True
2025-07-01 15:36:21 - DownloadService - INFO - 文件 128270.eps 需要加密下载
2025-07-01 15:36:21 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-07-01 15:36:22 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-07-01 15:36:23 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\128270.eps_20250701_153621.zip
2025-07-01 15:36:23 - DownloadService - INFO - 创建下载批次成功: 9a72dd74-d238-4600-bd07-d7ff97dcf0f5, 类型=single, 目标=file:706
2025-07-01 15:36:23 - DownloadService - INFO - 批次状态更新成功: 9a72dd74-d238-4600-bd07-d7ff97dcf0f5 -> ready
2025-07-01 15:36:23 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=9a72dd74-d238-4600-bd07-d7ff97dcf0f5, 加密=True
2025-07-01 15:36:23 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=706, 用户ID=1
2025-07-01 15:36:23 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=9a72dd74-d238-4600-bd07-d7ff97dcf0f5, 加密=True
2025-07-01 15:36:23 - DownloadService - INFO - 文件 128270.eps 需要加密下载
2025-07-01 15:36:23 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-07-01 15:36:24 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-07-01 15:36:25 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\128270.eps_20250701_153623.zip
2025-07-01 15:36:25 - DownloadService - INFO - 创建下载批次成功: 1bace260-c3de-4260-9697-5d6d3da9add1, 类型=single, 目标=file:706
2025-07-01 15:36:25 - DownloadService - INFO - 批次状态更新成功: 1bace260-c3de-4260-9697-5d6d3da9add1 -> ready
2025-07-01 15:36:25 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=1bace260-c3de-4260-9697-5d6d3da9add1, 加密=True
2025-07-01 15:36:25 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=706, 用户ID=1
2025-07-01 15:36:25 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=1bace260-c3de-4260-9697-5d6d3da9add1, 加密=True
2025-07-01 15:36:25 - DownloadService - INFO - 文件 128270.eps 需要加密下载
2025-07-01 15:36:25 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-07-01 15:36:27 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-07-01 15:36:28 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\128270.eps_20250701_153625.zip
2025-07-01 15:36:28 - DownloadService - INFO - 创建下载批次成功: 4dbfe58b-550d-4a3a-84d7-0fe9bc2c5801, 类型=single, 目标=file:706
2025-07-01 15:36:28 - DownloadService - INFO - 批次状态更新成功: 4dbfe58b-550d-4a3a-84d7-0fe9bc2c5801 -> ready
2025-07-01 15:36:28 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=4dbfe58b-550d-4a3a-84d7-0fe9bc2c5801, 加密=True
2025-07-01 15:36:28 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=706, 用户ID=1
2025-07-01 15:36:28 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=4dbfe58b-550d-4a3a-84d7-0fe9bc2c5801, 加密=True
2025-07-01 15:36:28 - DownloadService - INFO - 文件 128270.eps 需要加密下载
2025-07-01 15:36:28 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-07-01 15:36:29 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-07-01 15:36:30 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\128270.eps_20250701_153628.zip
2025-07-01 15:36:30 - DownloadService - INFO - 创建下载批次成功: 933cccc8-7cfa-4fcb-9307-4269be9a8995, 类型=single, 目标=file:706
2025-07-01 15:36:30 - DownloadService - INFO - 批次状态更新成功: 933cccc8-7cfa-4fcb-9307-4269be9a8995 -> ready
2025-07-01 15:36:30 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=933cccc8-7cfa-4fcb-9307-4269be9a8995, 加密=True
2025-07-01 15:36:30 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=706, 用户ID=1
2025-07-01 15:36:30 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=933cccc8-7cfa-4fcb-9307-4269be9a8995, 加密=True
2025-07-01 15:36:30 - DownloadService - INFO - 文件 128270.eps 需要加密下载
2025-07-01 15:36:30 - DownloadService - WARNING - pyminizip不可用，使用备用加密方案
2025-07-01 15:36:31 - DownloadService - WARNING - 7zip不可用: [WinError 2] 系统找不到指定的文件。，尝试其他方案
2025-07-01 15:36:32 - DownloadService - INFO - 使用zipfile成功创建加密ZIP文件: C:\Users\<USER>\Desktop\Net\backend\temp\downloads\128270.eps_20250701_153630.zip
2025-07-01 15:36:32 - DownloadService - INFO - 创建下载批次成功: 4cd9bc60-c8fe-4f76-a948-88964ce650bb, 类型=single, 目标=file:706
2025-07-01 15:36:32 - DownloadService - INFO - 批次状态更新成功: 4cd9bc60-c8fe-4f76-a948-88964ce650bb -> ready
2025-07-01 15:36:32 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=4cd9bc60-c8fe-4f76-a948-88964ce650bb, 加密=True
2025-07-01 15:36:32 - DownloadService - INFO - 保存密码申请记录成功: 文件ID=706, 用户ID=1
2025-07-01 15:36:32 - DownloadService - INFO - 记录下载成功: 文件ID=706, 用户ID=1, 类型=single, 批次=4cd9bc60-c8fe-4f76-a948-88964ce650bb, 加密=True
2025-07-01 15:36:42 - DownloadService - INFO - 创建下载批次成功: 6d277e39-032d-4a27-b93e-a133711f1bbd, 类型=batch, 目标=file:None
2025-07-01 15:36:42 - DownloadService - INFO - 批次状态更新成功: 6d277e39-032d-4a27-b93e-a133711f1bbd -> ready
2025-07-01 15:36:42 - DownloadService - INFO - 记录下载成功: 文件夹ID=None, 用户ID=1, 类型=batch, 批次=6d277e39-032d-4a27-b93e-a133711f1bbd, 加密=False
2025-07-01 15:39:11 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 15:44:15 - DownloadService - INFO - 创建下载批次成功: 36ad1011-ea8e-47f5-8e62-f17850cfa2da, 类型=batch, 目标=file:None
2025-07-01 15:44:15 - DownloadService - INFO - 批次状态更新成功: 36ad1011-ea8e-47f5-8e62-f17850cfa2da -> ready
2025-07-01 15:44:15 - DownloadService - INFO - 记录下载成功: 文件夹ID=None, 用户ID=1, 类型=batch, 批次=36ad1011-ea8e-47f5-8e62-f17850cfa2da, 加密=False
2025-07-01 15:44:40 - DownloadService - INFO - 创建下载批次成功: d8f1a022-3d94-4777-a55e-494692943a20, 类型=batch, 目标=file:None
2025-07-01 15:44:40 - DownloadService - INFO - 批次状态更新成功: d8f1a022-3d94-4777-a55e-494692943a20 -> ready
2025-07-01 15:44:40 - DownloadService - INFO - 记录下载成功: 文件夹ID=None, 用户ID=1, 类型=batch, 批次=d8f1a022-3d94-4777-a55e-494692943a20, 加密=False
2025-07-01 15:45:38 - DownloadService - INFO - 创建下载批次成功: 52e81909-6894-4e3a-90d4-5a7150eabfa7, 类型=batch, 目标=file:None
2025-07-01 15:45:38 - DownloadService - INFO - 批次状态更新成功: 52e81909-6894-4e3a-90d4-5a7150eabfa7 -> ready
2025-07-01 15:45:38 - DownloadService - INFO - 记录下载成功: 文件夹ID=None, 用户ID=1, 类型=batch, 批次=52e81909-6894-4e3a-90d4-5a7150eabfa7, 加密=False
2025-07-01 15:54:32 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 16:09:24 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 16:34:45 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 16:35:43 - DownloadService - INFO - 创建下载批次成功: 8a54c301-8213-44fc-b255-2e042eb77ffd, 类型=batch, 目标=file:None
2025-07-01 16:35:43 - DownloadService - INFO - 批次状态更新成功: 8a54c301-8213-44fc-b255-2e042eb77ffd -> ready
2025-07-01 16:35:43 - DownloadService - INFO - 记录下载成功: 文件夹ID=None, 用户ID=1, 类型=batch, 批次=8a54c301-8213-44fc-b255-2e042eb77ffd, 加密=False
2025-07-01 16:39:05 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 16:40:26 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 16:44:55 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 16:46:39 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 16:47:32 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 16:48:39 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 16:49:44 - DownloadService - INFO - 创建下载批次成功: 46cafcf9-f093-47e0-a12c-3a8fefd4df89, 类型=batch, 目标=file:None
2025-07-01 16:49:44 - DownloadService - INFO - 批次状态更新成功: 46cafcf9-f093-47e0-a12c-3a8fefd4df89 -> ready
2025-07-01 16:49:44 - DownloadService - INFO - 记录下载成功: 文件夹ID=None, 用户ID=1, 类型=batch, 批次=46cafcf9-f093-47e0-a12c-3a8fefd4df89, 加密=False
2025-07-01 16:51:43 - DownloadService - INFO - 创建下载批次成功: cf9ad4ce-da9d-46c5-802b-5ff53c3279a0, 类型=batch, 目标=file:None
2025-07-01 16:51:43 - DownloadService - INFO - 批次状态更新成功: cf9ad4ce-da9d-46c5-802b-5ff53c3279a0 -> ready
2025-07-01 16:51:43 - DownloadService - INFO - 记录下载成功: 文件夹ID=None, 用户ID=1, 类型=batch, 批次=cf9ad4ce-da9d-46c5-802b-5ff53c3279a0, 加密=False
2025-07-01 16:57:02 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 16:57:18 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 17:00:17 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 17:02:00 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 19:41:18 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 19:46:34 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-07-01 20:00:03 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
