2025-07-02 14:45:25 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-02 14:45:25 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-02 14:45:25 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-02 14:45:26 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-02 15:20:55 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-02 15:20:55 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-02 15:20:55 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-02 07:20:55 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-02 07:22:40 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 07:22:40 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}]}
2025-07-02 07:22:40 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-07-02 07:22:40 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 07:22:40 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}]}
2025-07-02 07:22:40 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 849
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 849, 文件信息: {'id': 849, 'folder_id': 13, 'filename': '0ad4b2c9b604d426 [转换].jpg', 'relative_path': '0ad4b2c9b604d426 [转换].jpg', 'file_size': 2587497, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-04-26T10:03:45.702794', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\0ad4b2c9b604d426 [转换].jpg', 'current_size': 2587497, 'current_modified': '2024-04-26T02:03:45.702794', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 0ad4b2c9b604d426 [转换].jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\0ad4b2c9b604d426 [转换].jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\0ad4b2c9b604d426 [转换].jpg, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\b76f2116f8f202ef3bc8b52357dbb743_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\b76f2116f8f202ef3bc8b52357dbb743_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\b76f2116f8f202ef3bc8b52357dbb743_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 850
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 850, 文件信息: {'id': 850, 'folder_id': 13, 'filename': '0b9f628a81f9d54b云.psd', 'relative_path': '0b9f628a81f9d54b云.psd', 'file_size': 102799092, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2020-11-14T23:01:19.086191', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\0b9f628a81f9d54b云.psd', 'current_size': 102799092, 'current_modified': '2020-11-14T15:01:19.086191', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 0b9f628a81f9d54b云.psd
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: psd, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\0b9f628a81f9d54b云.psd
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\0b9f628a81f9d54b云.psd, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\930354cc56ff95535ae9579ab23a77d0_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\930354cc56ff95535ae9579ab23a77d0_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\930354cc56ff95535ae9579ab23a77d0_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 851
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 851, 文件信息: {'id': 851, 'folder_id': 13, 'filename': '128270.eps', 'relative_path': '128270.eps', 'file_size': 25571174, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2021-08-06T01:26:16', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\128270.eps', 'current_size': 25571174, 'current_modified': '2021-08-05T17:26:16', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 128270.eps
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: eps, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\128270.eps
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\128270.eps, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\990fe8e3300f507ed23433a7df5d09cc_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\990fe8e3300f507ed23433a7df5d09cc_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\990fe8e3300f507ed23433a7df5d09cc_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 853
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 853, 文件信息: {'id': 853, 'folder_id': 13, 'filename': '411419桃花.eps', 'relative_path': '411419桃花.eps', 'file_size': 45763154, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2021-09-02T02:48:30', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\411419桃花.eps', 'current_size': 45763154, 'current_modified': '2021-09-01T18:48:30', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 411419桃花.eps
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: eps, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\411419桃花.eps
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\411419桃花.eps, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\9db07e37493c30f1593a4fcb79b3f7e5_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9db07e37493c30f1593a4fcb79b3f7e5_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9db07e37493c30f1593a4fcb79b3f7e5_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 854
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 854, 文件信息: {'id': 854, 'folder_id': 13, 'filename': '51c8461a4e86c.jpg', 'relative_path': '51c8461a4e86c.jpg', 'file_size': 563946, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-04-26T10:44:06.946036', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\51c8461a4e86c.jpg', 'current_size': 563946, 'current_modified': '2024-04-26T02:44:06.946036', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 51c8461a4e86c.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\51c8461a4e86c.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\51c8461a4e86c.jpg, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\1460777fa77dcf9387a9d11854c4415b_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1460777fa77dcf9387a9d11854c4415b_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1460777fa77dcf9387a9d11854c4415b_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 852
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 852, 文件信息: {'id': 852, 'folder_id': 13, 'filename': '2cb9461a36caebdc稻FC.tif', 'relative_path': '2cb9461a36caebdc稻FC.tif', 'file_size': 20947928, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-02-01T09:50:50.808548', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\2cb9461a36caebdc稻FC.tif', 'current_size': 20947928, 'current_modified': '2023-02-01T01:50:50.808548', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 2cb9461a36caebdc稻FC.tif
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: tif, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\2cb9461a36caebdc稻FC.tif
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\2cb9461a36caebdc稻FC.tif, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c62948fa504b919836fc587549d7be0d_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c62948fa504b919836fc587549d7be0d_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c62948fa504b919836fc587549d7be0d_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 855
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 855, 文件信息: {'id': 855, 'folder_id': 13, 'filename': '7e0c2edad4b42ec9.ai', 'relative_path': '7e0c2edad4b42ec9.ai', 'file_size': 2959255, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-09-13T08:43:06.906561', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\7e0c2edad4b42ec9.ai', 'current_size': 2959255, 'current_modified': '2024-09-13T00:43:06.906561', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 7e0c2edad4b42ec9.ai
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: ai, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\7e0c2edad4b42ec9.ai
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\7e0c2edad4b42ec9.ai, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\38edbb6847bc83f0bdd7a2f76f1c38f5_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\38edbb6847bc83f0bdd7a2f76f1c38f5_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\38edbb6847bc83f0bdd7a2f76f1c38f5_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 856
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 856, 文件信息: {'id': 856, 'folder_id': 13, 'filename': '901b8d4c38e1f8ca_12 [转换].ai', 'relative_path': '901b8d4c38e1f8ca_12 [转换].ai', 'file_size': 205605, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-09-13T08:41:44.119900', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\901b8d4c38e1f8ca_12 [转换].ai', 'current_size': 205605, 'current_modified': '2024-09-13T00:41:44.119900', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 901b8d4c38e1f8ca_12 [转换].ai
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: ai, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\901b8d4c38e1f8ca_12 [转换].ai
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\901b8d4c38e1f8ca_12 [转换].ai, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c5110db6e358939abf933f4304dcc540_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c5110db6e358939abf933f4304dcc540_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c5110db6e358939abf933f4304dcc540_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 857
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 857, 文件信息: {'id': 857, 'folder_id': 13, 'filename': '901b8d4c38e1f8ca_5 [转换].ai', 'relative_path': '901b8d4c38e1f8ca_5 [转换].ai', 'file_size': 1259031, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-09-13T08:43:58.207780', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\901b8d4c38e1f8ca_5 [转换].ai', 'current_size': 1259031, 'current_modified': '2024-09-13T00:43:58.207780', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 901b8d4c38e1f8ca_5 [转换].ai
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: ai, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\901b8d4c38e1f8ca_5 [转换].ai
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\901b8d4c38e1f8ca_5 [转换].ai, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\3e3ef8814b4336d264b87cfa3b1fb7f4_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3e3ef8814b4336d264b87cfa3b1fb7f4_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3e3ef8814b4336d264b87cfa3b1fb7f4_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 859
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 859, 文件信息: {'id': 859, 'folder_id': 13, 'filename': '9玉米大米丰收矢量立秋谷物粮食组合或粮仓和谷物组合.eps', 'relative_path': '9玉米大米丰收矢量立秋谷物粮食组合或粮仓和谷物组合.eps', 'file_size': 1460146, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-07-06T23:08:41.087426', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\9玉米大米丰收矢量立秋谷物粮食组合或粮仓和谷物组合.eps', 'current_size': 1460146, 'current_modified': '2022-07-06T15:08:41.087426', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 9玉米大米丰收矢量立秋谷物粮食组合或粮仓和谷物组合.eps
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: eps, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\9玉米大米丰收矢量立秋谷物粮食组合或粮仓和谷物组合.eps
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\9玉米大米丰收矢量立秋谷物粮食组合或粮仓和谷物组合.eps, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\3cbbdc5a677ec0770a8e9e05ef690795_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3cbbdc5a677ec0770a8e9e05ef690795_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3cbbdc5a677ec0770a8e9e05ef690795_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 858
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 858, 文件信息: {'id': 858, 'folder_id': 13, 'filename': '93551fa6a019619a.png', 'relative_path': '93551fa6a019619a.png', 'file_size': 3961376, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-30T11:54:42.047880', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\93551fa6a019619a.png', 'current_size': 3961376, 'current_modified': '2025-06-30T03:54:42.047880', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: 93551fa6a019619a.png
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\93551fa6a019619a.png
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\93551fa6a019619a.png, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d9ca0ade65f7f550ef5d9426bbfb4f0d_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d9ca0ade65f7f550ef5d9426bbfb4f0d_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d9ca0ade65f7f550ef5d9426bbfb4f0d_medium.jpg
2025-07-02 07:22:41 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 860
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件ID: 860, 文件信息: {'id': 860, 'folder_id': 13, 'filename': 'hellorf_hi2240650756.eps', 'relative_path': 'hellorf_hi2240650756.eps', 'file_size': 51768690, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-03-18T10:18:24.709406', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\hellorf_hi2240650756.eps', 'current_size': 51768690, 'current_modified': '2025-03-18T02:18:24.709406', 'exists': True}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:41 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件名: hellorf_hi2240650756.eps
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 文件扩展名: eps, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:41 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\hellorf_hi2240650756.eps
2025-07-02 07:22:41 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:41 - APIServer - INFO - 开始生成缩略图: C:\123\hellorf_hi2240650756.eps, 尺寸: medium
2025-07-02 07:22:41 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\716d92d8c131b0dea1645862c41d0a65_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\716d92d8c131b0dea1645862c41d0a65_medium.jpg
2025-07-02 07:22:41 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\716d92d8c131b0dea1645862c41d0a65_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 861
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 861, 文件信息: {'id': 861, 'folder_id': 13, 'filename': 'MZ05.psd', 'relative_path': 'MZ05.psd', 'file_size': 10976904, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-01-16T10:11:53.916110', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\MZ05.psd', 'current_size': 10976904, 'current_modified': '2024-01-16T02:11:53.916110', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: MZ05.psd
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: psd, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\MZ05.psd
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\MZ05.psd, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\0924320dbe215ff1b363e61e31345724_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0924320dbe215ff1b363e61e31345724_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0924320dbe215ff1b363e61e31345724_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 862
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 862, 文件信息: {'id': 862, 'folder_id': 13, 'filename': 'Nipic_12205446_20200727182155270000.ai', 'relative_path': 'Nipic_12205446_20200727182155270000.ai', 'file_size': 923912, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2020-07-27T18:21:57.720180', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\Nipic_12205446_20200727182155270000.ai', 'current_size': 923912, 'current_modified': '2020-07-27T10:21:57.720180', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: Nipic_12205446_20200727182155270000.ai
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: ai, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\Nipic_12205446_20200727182155270000.ai
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\Nipic_12205446_20200727182155270000.ai, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6cbe5cee3529e826dc18bd2fe39a3e08_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6cbe5cee3529e826dc18bd2fe39a3e08_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6cbe5cee3529e826dc18bd2fe39a3e08_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 863
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 863, 文件信息: {'id': 863, 'folder_id': 13, 'filename': 'Nipic_21319742_20201207181128478088.jpg', 'relative_path': 'Nipic_21319742_20201207181128478088.jpg', 'file_size': 2939970, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2020-12-07T18:11:30', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\Nipic_21319742_20201207181128478088.jpg', 'current_size': 2939970, 'current_modified': '2020-12-07T10:11:30', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: Nipic_21319742_20201207181128478088.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\Nipic_21319742_20201207181128478088.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\Nipic_21319742_20201207181128478088.jpg, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\22f453f8ea419b7f184b3855b6ddeaca_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22f453f8ea419b7f184b3855b6ddeaca_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22f453f8ea419b7f184b3855b6ddeaca_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 864
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 864, 文件信息: {'id': 864, 'folder_id': 13, 'filename': 'Nipic_31794491_20210726160624858126.jpg', 'relative_path': 'Nipic_31794491_20210726160624858126.jpg', 'file_size': 14059928, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2021-07-29T08:22:24', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\Nipic_31794491_20210726160624858126.jpg', 'current_size': 14059928, 'current_modified': '2021-07-29T00:22:24', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: Nipic_31794491_20210726160624858126.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\Nipic_31794491_20210726160624858126.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\Nipic_31794491_20210726160624858126.jpg, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\fa6abf1cb32d2c80673f6958595625c4_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fa6abf1cb32d2c80673f6958595625c4_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fa6abf1cb32d2c80673f6958595625c4_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 865
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 865, 文件信息: {'id': 865, 'folder_id': 13, 'filename': 'Nipic_32283820_20210726145305629128.jpg', 'relative_path': 'Nipic_32283820_20210726145305629128.jpg', 'file_size': 2127742, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2021-07-29T08:23:00', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\Nipic_32283820_20210726145305629128.jpg', 'current_size': 2127742, 'current_modified': '2021-07-29T00:23:00', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: Nipic_32283820_20210726145305629128.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\Nipic_32283820_20210726145305629128.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\Nipic_32283820_20210726145305629128.jpg, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\9ff38ac4063fc0965aeb7d5f579d1573_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9ff38ac4063fc0965aeb7d5f579d1573_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9ff38ac4063fc0965aeb7d5f579d1573_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 866
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 866, 文件信息: {'id': 866, 'folder_id': 13, 'filename': 'Nipic_34286065_20240206110731220.eps', 'relative_path': 'Nipic_34286065_20240206110731220.eps', 'file_size': 2332006, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-02-19T05:05:08', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\Nipic_34286065_20240206110731220.eps', 'current_size': 2332006, 'current_modified': '2024-02-18T21:05:08', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: Nipic_34286065_20240206110731220.eps
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: eps, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\Nipic_34286065_20240206110731220.eps
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\Nipic_34286065_20240206110731220.eps, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\9236f4e998b266516b12deeb88944dc2_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9236f4e998b266516b12deeb88944dc2_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9236f4e998b266516b12deeb88944dc2_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 867
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 867, 文件信息: {'id': 867, 'folder_id': 13, 'filename': 'Nipic_34407361_20240807143228919.jpg', 'relative_path': 'Nipic_34407361_20240807143228919.jpg', 'file_size': 3121552, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-08-09T02:36:00', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\Nipic_34407361_20240807143228919.jpg', 'current_size': 3121552, 'current_modified': '2024-08-08T18:36:00', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: Nipic_34407361_20240807143228919.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\Nipic_34407361_20240807143228919.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\Nipic_34407361_20240807143228919.jpg, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f9e707521eed44cfdb2330cffd5a1e93_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f9e707521eed44cfdb2330cffd5a1e93_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f9e707521eed44cfdb2330cffd5a1e93_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 868
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 868, 文件信息: {'id': 868, 'folder_id': 13, 'filename': '乡村振兴22.jpg', 'relative_path': '乡村振兴22.jpg', 'file_size': 4610118, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-03-18T18:22:47.411272', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\乡村振兴22.jpg', 'current_size': 4610118, 'current_modified': '2023-03-18T10:22:47.411272', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: 乡村振兴22.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\乡村振兴22.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\乡村振兴22.jpg, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\e85c238c88dede0cfa25aeee4033e9c1_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\e85c238c88dede0cfa25aeee4033e9c1_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\e85c238c88dede0cfa25aeee4033e9c1_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 869
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 869, 文件信息: {'id': 869, 'folder_id': 13, 'filename': '六连图 (3).jpg', 'relative_path': '六连图 (3).jpg', 'file_size': 494657, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2020-09-01T16:36:44.867268', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\六连图 (3).jpg', 'current_size': 494657, 'current_modified': '2020-09-01T08:36:44.867268', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: 六连图 (3).jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\六连图 (3).jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\六连图 (3).jpg, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\4a5f7d1f3e880e122a0b588334849a61_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\4a5f7d1f3e880e122a0b588334849a61_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\4a5f7d1f3e880e122a0b588334849a61_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 870
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 870, 文件信息: {'id': 870, 'folder_id': 13, 'filename': '国潮喜庆十一庆祝国庆节爱国活动拍照道具手举牌.eps', 'relative_path': '国潮喜庆十一庆祝国庆节爱国活动拍照道具手举牌.eps', 'file_size': 2971686, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-08-24T20:16:24', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\国潮喜庆十一庆祝国庆节爱国活动拍照道具手举牌.eps', 'current_size': 2971686, 'current_modified': '2023-08-24T12:16:24', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: 国潮喜庆十一庆祝国庆节爱国活动拍照道具手举牌.eps
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: eps, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\国潮喜庆十一庆祝国庆节爱国活动拍照道具手举牌.eps
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\国潮喜庆十一庆祝国庆节爱国活动拍照道具手举牌.eps, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\650f758e2356246e5004e668d404afa5_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\650f758e2356246e5004e668d404afa5_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\650f758e2356246e5004e668d404afa5_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 871
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 871, 文件信息: {'id': 871, 'folder_id': 13, 'filename': '大米2 米袋.psd', 'relative_path': '大米2 米袋.psd', 'file_size': 17533752, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2019-09-07T13:16:04', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\大米2 米袋.psd', 'current_size': 17533752, 'current_modified': '2019-09-07T05:16:04', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: 大米2 米袋.psd
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: psd, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\大米2 米袋.psd
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\大米2 米袋.psd, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\be03ed15d1f6572be7a4a95f7981dd94_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\be03ed15d1f6572be7a4a95f7981dd94_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\be03ed15d1f6572be7a4a95f7981dd94_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 872
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 872, 文件信息: {'id': 872, 'folder_id': 13, 'filename': '立春V18.jpg', 'relative_path': '立春V18.jpg', 'file_size': 1626769, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-01-13T00:15:36.737895', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\立春V18.jpg', 'current_size': 1626769, 'current_modified': '2023-01-12T16:15:36.737895', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: 立春V18.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\立春V18.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\立春V18.jpg, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\e8321d1789b381a173fe72f4e3250936_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\e8321d1789b381a173fe72f4e3250936_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\e8321d1789b381a173fe72f4e3250936_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 873
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 873, 文件信息: {'id': 873, 'folder_id': 13, 'filename': '簸箕.tif', 'relative_path': '簸箕.tif', 'file_size': 6424760, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-09-13T08:31:26.823580', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\簸箕.tif', 'current_size': 6424760, 'current_modified': '2024-09-13T00:31:26.823580', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: 簸箕.tif
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: tif, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\簸箕.tif
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\簸箕.tif, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2ee8d5bceb7ee53e83a555fc0aa184d5_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2ee8d5bceb7ee53e83a555fc0aa184d5_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2ee8d5bceb7ee53e83a555fc0aa184d5_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 874
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 874, 文件信息: {'id': 874, 'folder_id': 13, 'filename': '系列图1.jpg', 'relative_path': '系列图1.jpg', 'file_size': 3511876, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-09-25T17:54:08', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\系列图1.jpg', 'current_size': 3511876, 'current_modified': '2022-09-25T09:54:08', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: 系列图1.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\系列图1.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\系列图1.jpg, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\1eaa2cd66af550699f1a37d395f14053_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1eaa2cd66af550699f1a37d395f14053_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1eaa2cd66af550699f1a37d395f14053_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 875
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 875, 文件信息: {'id': 875, 'folder_id': 13, 'filename': '绿色花纹背景.jpg', 'relative_path': '绿色花纹背景.jpg', 'file_size': 1329347, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-07-01T14:00:23.889590', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\绿色花纹背景.jpg', 'current_size': 1329347, 'current_modified': '2022-07-01T06:00:23.889590', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: 绿色花纹背景.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\绿色花纹背景.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\绿色花纹背景.jpg, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\be9a11837c84f8705b70b12e3833e883_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\be9a11837c84f8705b70b12e3833e883_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\be9a11837c84f8705b70b12e3833e883_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 876
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 876, 文件信息: {'id': 876, 'folder_id': 13, 'filename': '老虎Nipic_34591069_20230410231931533.jpg', 'relative_path': '老虎Nipic_34591069_20230410231931533.jpg', 'file_size': 2711584, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-04-12T01:39:10', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\老虎Nipic_34591069_20230410231931533.jpg', 'current_size': 2711584, 'current_modified': '2023-04-11T17:39:10', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: 老虎Nipic_34591069_20230410231931533.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\老虎Nipic_34591069_20230410231931533.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\老虎Nipic_34591069_20230410231931533.jpg, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\da55d465da850332c72b9523e3ac95b0_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\da55d465da850332c72b9523e3ac95b0_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\da55d465da850332c72b9523e3ac95b0_medium.jpg
2025-07-02 07:22:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 877
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件ID: 877, 文件信息: {'id': 877, 'folder_id': 13, 'filename': '谷雨4.psd', 'relative_path': '谷雨4.psd', 'file_size': 128826638, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-04-12T21:36:10', 'created_at': '2025-07-01T08:48:51', 'last_accessed': None}, 'full_path': 'C:\\123\\谷雨4.psd', 'current_size': 128826638, 'current_modified': '2022-04-12T13:36:10', 'exists': True}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 13
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:22:42 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件名: 谷雨4.psd
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: psd, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:22:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\123\谷雨4.psd
2025-07-02 07:22:42 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:22:42 - APIServer - INFO - 开始生成缩略图: C:\123\谷雨4.psd, 尺寸: medium
2025-07-02 07:22:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\8771645ccb2af7c2428167a5e7d587a2_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8771645ccb2af7c2428167a5e7d587a2_medium.jpg
2025-07-02 07:22:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8771645ccb2af7c2428167a5e7d587a2_medium.jpg
2025-07-02 07:23:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 07:23:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 07:23:43 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 07:23:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 07:23:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 07:23:43 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 07:23:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 07:23:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 07:23:43 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 07:23:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 07:23:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 07:23:43 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 07:23:45 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1195
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件ID: 1195, 文件信息: {'id': 1195, 'folder_id': 15, 'filename': '乡村振兴22.jpg', 'relative_path': '2\\乡村振兴22.jpg', 'file_size': 4610118, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-03-18T10:22:47.411272', 'created_at': '2025-07-02T07:23:35', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\乡村振兴22.jpg', 'current_size': 4610118, 'current_modified': '2023-03-18T10:22:47.411272', 'exists': True}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 15
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件名: 乡村振兴22.jpg
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\乡村振兴22.jpg
2025-07-02 07:23:45 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:23:45 - APIServer - INFO - 开始生成缩略图: C:\321\2\乡村振兴22.jpg, 尺寸: medium
2025-07-02 07:23:45 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-02 07:23:45 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-02 07:23:45 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-02 07:23:45 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1196
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件ID: 1196, 文件信息: {'id': 1196, 'folder_id': 15, 'filename': '系列图1.jpg', 'relative_path': '2\\系列图1.jpg', 'file_size': 3511876, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-09-25T09:54:08', 'created_at': '2025-07-02T07:23:35', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\系列图1.jpg', 'current_size': 3511876, 'current_modified': '2022-09-25T09:54:08', 'exists': True}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 15
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件名: 系列图1.jpg
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\系列图1.jpg
2025-07-02 07:23:45 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:23:45 - APIServer - INFO - 开始生成缩略图: C:\321\2\系列图1.jpg, 尺寸: medium
2025-07-02 07:23:45 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-02 07:23:45 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-02 07:23:45 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-02 07:23:45 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1194
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件ID: 1194, 文件信息: {'id': 1194, 'folder_id': 15, 'filename': '0ad4b2c9b604d426 [转换].jpg', 'relative_path': '0ad4b2c9b604d426 [转换].jpg', 'file_size': 2587497, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-04-26T02:03:45.702794', 'created_at': '2025-07-02T07:23:35', 'last_accessed': None}, 'full_path': 'C:\\321\\0ad4b2c9b604d426 [转换].jpg', 'current_size': 2587497, 'current_modified': '2024-04-26T02:03:45.702794', 'exists': True}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 15
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-02 07:23:45 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件名: 0ad4b2c9b604d426 [转换].jpg
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-02 07:23:45 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\0ad4b2c9b604d426 [转换].jpg
2025-07-02 07:23:45 - APIServer - INFO - 缩略图服务可用: True
2025-07-02 07:23:45 - APIServer - INFO - 开始生成缩略图: C:\321\0ad4b2c9b604d426 [转换].jpg, 尺寸: medium
2025-07-02 07:23:45 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\13fd8841a29c03f9ebc5c9fb71522397_medium.jpg
2025-07-02 07:23:45 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\13fd8841a29c03f9ebc5c9fb71522397_medium.jpg
2025-07-02 07:23:45 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\13fd8841a29c03f9ebc5c9fb71522397_medium.jpg
2025-07-02 07:23:52 - APIServer - INFO - 批量下载: user_info={'user_id': 1, 'username': 'admin', 'permissions': ['read', 'write', 'delete', 'admin', 'upload', 'download'], 'is_admin': True}, user_id=1, file_ids=['1194', '1195', '1196']
2025-07-02 07:23:52 - APIServer - INFO - === 调试模式：收到下载记录请求 ===
2025-07-02 07:23:52 - APIServer - INFO - 收到的token: 5TG_JoNTQbpb4w9RNENC...
2025-07-02 07:23:52 - APIServer - INFO - 用户验证结果: {'user_id': 1, 'username': 'admin', 'permissions': ['read', 'write', 'delete', 'admin', 'upload', 'download'], 'is_admin': True}
2025-07-02 07:23:52 - APIServer - INFO - 调用下载服务获取用户 1 的记录，页码: 1, 每页: 20, 筛选条件: {}
2025-07-02 07:23:52 - APIServer - INFO - 下载服务返回结果: {'success': True, 'records': [{'id': 106, 'file_id': None, 'folder_id': None, 'batch_id': 74, 'session_id': None, 'filename': '批量下载 (3 个文件)', 'file_size': 10307881, 'download_time': '2025-07-02T07:23:52.882343', 'created_at': '2025-07-02T07:23:52', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250702_072352.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '67985eba-654e-403d-8ac6-679ca09962cd', 'batch_type': 'batch', 'download_name': '批量下载_3个文件', 'total_files': 3, 'total_size': 10709491, 'status': 'ready', 'created_at': '2025-07-02T07:23:52', 'completed_at': None}, 'file_count': 3, 'folder_name': None}, {'id': 105, 'file_id': None, 'folder_id': None, 'batch_id': 73, 'session_id': None, 'filename': '批量下载 (7 个文件)', 'file_size': 117823359, 'download_time': '2025-07-01T16:51:43.248615', 'created_at': '2025-07-01T08:51:43', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_165133.zip', 'download_status': 'completed', 'batch_info': {'batch_id': 'cf9ad4ce-da9d-46c5-802b-5ff53c3279a0', 'batch_type': 'batch', 'download_name': '批量下载_7个文件', 'total_files': 7, 'total_size': 206647239, 'status': 'ready', 'created_at': '2025-07-01T08:51:43', 'completed_at': None}, 'file_count': 7, 'folder_name': None}, {'id': 104, 'file_id': None, 'folder_id': None, 'batch_id': 72, 'session_id': None, 'filename': '批量下载 (3 个文件)', 'file_size': 5779610, 'download_time': '2025-07-01T16:49:44.537228', 'created_at': '2025-07-01T08:49:44', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_164944.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '46cafcf9-f093-47e0-a12c-3a8fefd4df89', 'batch_type': 'batch', 'download_name': '批量下载_3个文件', 'total_files': 3, 'total_size': 6110698, 'status': 'ready', 'created_at': '2025-07-01T08:49:44', 'completed_at': None}, 'file_count': 3, 'folder_name': None}, {'id': 103, 'file_id': None, 'folder_id': None, 'batch_id': 71, 'session_id': None, 'filename': '批量下载 (5 个文件)', 'file_size': 93085010, 'download_time': '2025-07-01T16:35:43.808291', 'created_at': '2025-07-01T08:35:43', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_163537.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '8a54c301-8213-44fc-b255-2e042eb77ffd', 'batch_type': 'batch', 'download_name': '批量下载_5个文件', 'total_files': 5, 'total_size': 152111296, 'status': 'ready', 'created_at': '2025-07-01T08:35:43', 'completed_at': None}, 'file_count': 5, 'folder_name': None}, {'id': 102, 'file_id': None, 'folder_id': None, 'batch_id': 70, 'session_id': None, 'filename': '批量下载 (3 个文件)', 'file_size': 19438082, 'download_time': '2025-07-01T15:45:38.792161', 'created_at': '2025-07-01T07:45:38', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_154538.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '52e81909-6894-4e3a-90d4-5a7150eabfa7', 'batch_type': 'batch', 'download_name': '批量下载_3个文件', 'total_files': 3, 'total_size': 23741030, 'status': 'ready', 'created_at': '2025-07-01T07:45:38', 'completed_at': None}, 'file_count': 3, 'folder_name': None}, {'id': 101, 'file_id': None, 'folder_id': None, 'batch_id': 69, 'session_id': None, 'filename': '批量下载 (2 个文件)', 'file_size': 14444991, 'download_time': '2025-07-01T15:44:40.300261', 'created_at': '2025-07-01T07:44:40', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_154439.zip', 'download_status': 'completed', 'batch_info': {'batch_id': 'd8f1a022-3d94-4777-a55e-494692943a20', 'batch_type': 'batch', 'download_name': '批量下载_2个文件', 'total_files': 2, 'total_size': 28158671, 'status': 'ready', 'created_at': '2025-07-01T07:44:40', 'completed_at': None}, 'file_count': 2, 'folder_name': None}, {'id': 100, 'file_id': None, 'folder_id': None, 'batch_id': 68, 'session_id': None, 'filename': '批量下载 (1 个文件)', 'file_size': 16917000, 'download_time': '2025-07-01T15:44:15.792357', 'created_at': '2025-07-01T07:44:15', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_154415.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '36ad1011-ea8e-47f5-8e62-f17850cfa2da', 'batch_type': 'batch', 'download_name': '批量下载_1个文件', 'total_files': 1, 'total_size': 20947928, 'status': 'ready', 'created_at': '2025-07-01T07:44:15', 'completed_at': None}, 'file_count': 1, 'folder_name': None}, {'id': 99, 'file_id': None, 'folder_id': None, 'batch_id': 67, 'session_id': None, 'filename': '批量下载 (1 个文件)', 'file_size': 16917000, 'download_time': '2025-07-01T15:36:42.794644', 'created_at': '2025-07-01T07:36:42', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_153642.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '6d277e39-032d-4a27-b93e-a133711f1bbd', 'batch_type': 'batch', 'download_name': '批量下载_1个文件', 'total_files': 1, 'total_size': 20947928, 'status': 'ready', 'created_at': '2025-07-01T07:36:42', 'completed_at': None}, 'file_count': 1, 'folder_name': None}, {'id': 98, 'file_id': 706, 'folder_id': None, 'batch_id': 66, 'session_id': None, 'filename': '128270.eps_20250701_153630.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:32.673865', 'created_at': '2025-07-01T07:36:32', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'P^qpITmN70hr', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153630.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '4cd9bc60-c8fe-4f76-a948-88964ce650bb', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:32', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 97, 'file_id': 706, 'folder_id': None, 'batch_id': 66, 'session_id': None, 'filename': '128270.eps_20250701_153630.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:32.658658', 'created_at': '2025-07-01T07:36:32', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'P^qpITmN70hr', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153630.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '4cd9bc60-c8fe-4f76-a948-88964ce650bb', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:32', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 96, 'file_id': 706, 'folder_id': None, 'batch_id': 65, 'session_id': None, 'filename': '128270.eps_20250701_153628.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:30.370965', 'created_at': '2025-07-01T07:36:30', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'DGgfKUm2#l7*', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153628.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '933cccc8-7cfa-4fcb-9307-4269be9a8995', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:30', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 95, 'file_id': 706, 'folder_id': None, 'batch_id': 65, 'session_id': None, 'filename': '128270.eps_20250701_153628.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:30.355878', 'created_at': '2025-07-01T07:36:30', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'DGgfKUm2#l7*', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153628.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '933cccc8-7cfa-4fcb-9307-4269be9a8995', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:30', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 94, 'file_id': 706, 'folder_id': None, 'batch_id': 64, 'session_id': None, 'filename': '128270.eps_20250701_153625.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:28.156487', 'created_at': '2025-07-01T07:36:28', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': '5VMbvK*J#p&w', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153625.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '4dbfe58b-550d-4a3a-84d7-0fe9bc2c5801', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:28', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 93, 'file_id': 706, 'folder_id': None, 'batch_id': 64, 'session_id': None, 'filename': '128270.eps_20250701_153625.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:28.139956', 'created_at': '2025-07-01T07:36:28', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': '5VMbvK*J#p&w', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153625.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '4dbfe58b-550d-4a3a-84d7-0fe9bc2c5801', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:28', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 92, 'file_id': 706, 'folder_id': None, 'batch_id': 63, 'session_id': None, 'filename': '128270.eps_20250701_153623.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:25.873281', 'created_at': '2025-07-01T07:36:25', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'NjI5j*IJ5YrG', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153623.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '1bace260-c3de-4260-9697-5d6d3da9add1', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:25', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 91, 'file_id': 706, 'folder_id': None, 'batch_id': 63, 'session_id': None, 'filename': '128270.eps_20250701_153623.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:25.840279', 'created_at': '2025-07-01T07:36:25', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'NjI5j*IJ5YrG', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153623.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '1bace260-c3de-4260-9697-5d6d3da9add1', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:25', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 90, 'file_id': 706, 'folder_id': None, 'batch_id': 62, 'session_id': None, 'filename': '128270.eps_20250701_153621.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:23.501076', 'created_at': '2025-07-01T07:36:23', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': '$2Tqy4r*Ilz*', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153621.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '9a72dd74-d238-4600-bd07-d7ff97dcf0f5', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:23', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 89, 'file_id': 706, 'folder_id': None, 'batch_id': 62, 'session_id': None, 'filename': '128270.eps_20250701_153621.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:23.485076', 'created_at': '2025-07-01T07:36:23', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': '$2Tqy4r*Ilz*', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153621.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '9a72dd74-d238-4600-bd07-d7ff97dcf0f5', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:23', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 88, 'file_id': 706, 'folder_id': None, 'batch_id': 61, 'session_id': None, 'filename': '128270.eps_20250701_153618.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:21.215561', 'created_at': '2025-07-01T07:36:21', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'GVN&8808Wn%K', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153618.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '2082a02d-fa2d-4e07-b46c-c673dd3e500e', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:21', 'completed_at': None}, 'file_count': 0, 'folder_name': None}, {'id': 87, 'file_id': 706, 'folder_id': None, 'batch_id': 61, 'session_id': None, 'filename': '128270.eps_20250701_153618.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:21.198440', 'created_at': '2025-07-01T07:36:21', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'GVN&8808Wn%K', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153618.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '2082a02d-fa2d-4e07-b46c-c673dd3e500e', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:21', 'completed_at': None}, 'file_count': 0, 'folder_name': None}], 'total': 81, 'page': 1, 'limit': 20}
2025-07-02 07:23:52 - APIServer - INFO - 获取到 20 条原始记录
2025-07-02 07:23:52 - APIServer - INFO - 处理第 1 条记录: {'id': 106, 'file_id': None, 'folder_id': None, 'batch_id': 74, 'session_id': None, 'filename': '批量下载 (3 个文件)', 'file_size': 10307881, 'download_time': '2025-07-02T07:23:52.882343', 'created_at': '2025-07-02T07:23:52', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250702_072352.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '67985eba-654e-403d-8ac6-679ca09962cd', 'batch_type': 'batch', 'download_name': '批量下载_3个文件', 'total_files': 3, 'total_size': 10709491, 'status': 'ready', 'created_at': '2025-07-02T07:23:52', 'completed_at': None}, 'file_count': 3, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 2 条记录: {'id': 105, 'file_id': None, 'folder_id': None, 'batch_id': 73, 'session_id': None, 'filename': '批量下载 (7 个文件)', 'file_size': 117823359, 'download_time': '2025-07-01T16:51:43.248615', 'created_at': '2025-07-01T08:51:43', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_165133.zip', 'download_status': 'completed', 'batch_info': {'batch_id': 'cf9ad4ce-da9d-46c5-802b-5ff53c3279a0', 'batch_type': 'batch', 'download_name': '批量下载_7个文件', 'total_files': 7, 'total_size': 206647239, 'status': 'ready', 'created_at': '2025-07-01T08:51:43', 'completed_at': None}, 'file_count': 7, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 3 条记录: {'id': 104, 'file_id': None, 'folder_id': None, 'batch_id': 72, 'session_id': None, 'filename': '批量下载 (3 个文件)', 'file_size': 5779610, 'download_time': '2025-07-01T16:49:44.537228', 'created_at': '2025-07-01T08:49:44', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_164944.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '46cafcf9-f093-47e0-a12c-3a8fefd4df89', 'batch_type': 'batch', 'download_name': '批量下载_3个文件', 'total_files': 3, 'total_size': 6110698, 'status': 'ready', 'created_at': '2025-07-01T08:49:44', 'completed_at': None}, 'file_count': 3, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 4 条记录: {'id': 103, 'file_id': None, 'folder_id': None, 'batch_id': 71, 'session_id': None, 'filename': '批量下载 (5 个文件)', 'file_size': 93085010, 'download_time': '2025-07-01T16:35:43.808291', 'created_at': '2025-07-01T08:35:43', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_163537.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '8a54c301-8213-44fc-b255-2e042eb77ffd', 'batch_type': 'batch', 'download_name': '批量下载_5个文件', 'total_files': 5, 'total_size': 152111296, 'status': 'ready', 'created_at': '2025-07-01T08:35:43', 'completed_at': None}, 'file_count': 5, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 5 条记录: {'id': 102, 'file_id': None, 'folder_id': None, 'batch_id': 70, 'session_id': None, 'filename': '批量下载 (3 个文件)', 'file_size': 19438082, 'download_time': '2025-07-01T15:45:38.792161', 'created_at': '2025-07-01T07:45:38', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_154538.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '52e81909-6894-4e3a-90d4-5a7150eabfa7', 'batch_type': 'batch', 'download_name': '批量下载_3个文件', 'total_files': 3, 'total_size': 23741030, 'status': 'ready', 'created_at': '2025-07-01T07:45:38', 'completed_at': None}, 'file_count': 3, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 6 条记录: {'id': 101, 'file_id': None, 'folder_id': None, 'batch_id': 69, 'session_id': None, 'filename': '批量下载 (2 个文件)', 'file_size': 14444991, 'download_time': '2025-07-01T15:44:40.300261', 'created_at': '2025-07-01T07:44:40', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_154439.zip', 'download_status': 'completed', 'batch_info': {'batch_id': 'd8f1a022-3d94-4777-a55e-494692943a20', 'batch_type': 'batch', 'download_name': '批量下载_2个文件', 'total_files': 2, 'total_size': 28158671, 'status': 'ready', 'created_at': '2025-07-01T07:44:40', 'completed_at': None}, 'file_count': 2, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 7 条记录: {'id': 100, 'file_id': None, 'folder_id': None, 'batch_id': 68, 'session_id': None, 'filename': '批量下载 (1 个文件)', 'file_size': 16917000, 'download_time': '2025-07-01T15:44:15.792357', 'created_at': '2025-07-01T07:44:15', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_154415.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '36ad1011-ea8e-47f5-8e62-f17850cfa2da', 'batch_type': 'batch', 'download_name': '批量下载_1个文件', 'total_files': 1, 'total_size': 20947928, 'status': 'ready', 'created_at': '2025-07-01T07:44:15', 'completed_at': None}, 'file_count': 1, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 8 条记录: {'id': 99, 'file_id': None, 'folder_id': None, 'batch_id': 67, 'session_id': None, 'filename': '批量下载 (1 个文件)', 'file_size': 16917000, 'download_time': '2025-07-01T15:36:42.794644', 'created_at': '2025-07-01T07:36:42', 'download_type': '批量下载', 'download_type_raw': 'batch', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': False, 'has_password': False, 'password': None, 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': 'batch_download_20250701_153642.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '6d277e39-032d-4a27-b93e-a133711f1bbd', 'batch_type': 'batch', 'download_name': '批量下载_1个文件', 'total_files': 1, 'total_size': 20947928, 'status': 'ready', 'created_at': '2025-07-01T07:36:42', 'completed_at': None}, 'file_count': 1, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 9 条记录: {'id': 98, 'file_id': 706, 'folder_id': None, 'batch_id': 66, 'session_id': None, 'filename': '128270.eps_20250701_153630.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:32.673865', 'created_at': '2025-07-01T07:36:32', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'P^qpITmN70hr', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153630.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '4cd9bc60-c8fe-4f76-a948-88964ce650bb', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:32', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 10 条记录: {'id': 97, 'file_id': 706, 'folder_id': None, 'batch_id': 66, 'session_id': None, 'filename': '128270.eps_20250701_153630.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:32.658658', 'created_at': '2025-07-01T07:36:32', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'P^qpITmN70hr', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153630.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '4cd9bc60-c8fe-4f76-a948-88964ce650bb', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:32', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 11 条记录: {'id': 96, 'file_id': 706, 'folder_id': None, 'batch_id': 65, 'session_id': None, 'filename': '128270.eps_20250701_153628.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:30.370965', 'created_at': '2025-07-01T07:36:30', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'DGgfKUm2#l7*', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153628.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '933cccc8-7cfa-4fcb-9307-4269be9a8995', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:30', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 12 条记录: {'id': 95, 'file_id': 706, 'folder_id': None, 'batch_id': 65, 'session_id': None, 'filename': '128270.eps_20250701_153628.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:30.355878', 'created_at': '2025-07-01T07:36:30', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'DGgfKUm2#l7*', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153628.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '933cccc8-7cfa-4fcb-9307-4269be9a8995', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:30', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 13 条记录: {'id': 94, 'file_id': 706, 'folder_id': None, 'batch_id': 64, 'session_id': None, 'filename': '128270.eps_20250701_153625.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:28.156487', 'created_at': '2025-07-01T07:36:28', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': '5VMbvK*J#p&w', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153625.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '4dbfe58b-550d-4a3a-84d7-0fe9bc2c5801', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:28', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 14 条记录: {'id': 93, 'file_id': 706, 'folder_id': None, 'batch_id': 64, 'session_id': None, 'filename': '128270.eps_20250701_153625.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:28.139956', 'created_at': '2025-07-01T07:36:28', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': '5VMbvK*J#p&w', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153625.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '4dbfe58b-550d-4a3a-84d7-0fe9bc2c5801', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:28', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 15 条记录: {'id': 92, 'file_id': 706, 'folder_id': None, 'batch_id': 63, 'session_id': None, 'filename': '128270.eps_20250701_153623.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:25.873281', 'created_at': '2025-07-01T07:36:25', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'NjI5j*IJ5YrG', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153623.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '1bace260-c3de-4260-9697-5d6d3da9add1', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:25', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 16 条记录: {'id': 91, 'file_id': 706, 'folder_id': None, 'batch_id': 63, 'session_id': None, 'filename': '128270.eps_20250701_153623.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:25.840279', 'created_at': '2025-07-01T07:36:25', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'NjI5j*IJ5YrG', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153623.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '1bace260-c3de-4260-9697-5d6d3da9add1', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:25', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 17 条记录: {'id': 90, 'file_id': 706, 'folder_id': None, 'batch_id': 62, 'session_id': None, 'filename': '128270.eps_20250701_153621.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:23.501076', 'created_at': '2025-07-01T07:36:23', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': '$2Tqy4r*Ilz*', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153621.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '9a72dd74-d238-4600-bd07-d7ff97dcf0f5', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:23', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 18 条记录: {'id': 89, 'file_id': 706, 'folder_id': None, 'batch_id': 62, 'session_id': None, 'filename': '128270.eps_20250701_153621.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:23.485076', 'created_at': '2025-07-01T07:36:23', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': '$2Tqy4r*Ilz*', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153621.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '9a72dd74-d238-4600-bd07-d7ff97dcf0f5', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:23', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 19 条记录: {'id': 88, 'file_id': 706, 'folder_id': None, 'batch_id': 61, 'session_id': None, 'filename': '128270.eps_20250701_153618.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:21.215561', 'created_at': '2025-07-01T07:36:21', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'GVN&8808Wn%K', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153618.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '2082a02d-fa2d-4e07-b46c-c673dd3e500e', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:21', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 处理第 20 条记录: {'id': 87, 'file_id': 706, 'folder_id': None, 'batch_id': 61, 'session_id': None, 'filename': '128270.eps_20250701_153618.zip', 'file_size': 12115442, 'download_time': '2025-07-01T15:36:21.198440', 'created_at': '2025-07-01T07:36:21', 'download_type': '单文件', 'download_type_raw': 'single', 'download_source': 'web', 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'is_encrypted': True, 'has_password': True, 'password': 'GVN&8808Wn%K', 'download_count': 0, 'encrypted_download_count': 0, 'first_download_time': None, 'last_download_time': None, 'zip_filename': '128270.eps_20250701_153618.zip', 'download_status': 'completed', 'batch_info': {'batch_id': '2082a02d-fa2d-4e07-b46c-c673dd3e500e', 'batch_type': 'single', 'download_name': '128270.eps', 'total_files': 1, 'total_size': 25571174, 'status': 'ready', 'created_at': '2025-07-01T07:36:21', 'completed_at': None}, 'file_count': 0, 'folder_name': None}
2025-07-02 07:23:52 - APIServer - INFO - 返回 20 条简化记录
2025-07-02 07:56:51 - APIServer - INFO - API服务器已停止
2025-07-02 16:21:52 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-02 16:21:52 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-02 16:21:52 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-02 08:21:53 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-02 08:21:56 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:21:56 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:21:56 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:21:56 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:21:57 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:21:57 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:22:20 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:22:20 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:22:20 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:22:20 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:22:20 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:22:20 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:22:48 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:22:48 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:22:48 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:22:48 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:22:48 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:22:48 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:23:19 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:23:19 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:23:19 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:23:19 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:23:19 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:23:19 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:23:37 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:23:37 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:23:37 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:23:37 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:23:37 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:23:37 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 16:26:31 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-02 16:26:31 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-02 16:26:31 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-02 08:26:31 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-02 08:26:31 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-07-02 08:27:09 - APIServer - INFO - API服务器已停止
2025-07-02 16:27:23 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-02 16:27:23 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-02 16:27:23 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-02 08:27:23 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-02 08:29:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:29:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:29:17 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:29:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:29:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:29:17 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:29:29 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:29:30 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:29:30 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:29:30 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:29:30 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:29:30 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:29:56 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:29:56 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:29:56 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:29:56 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:29:56 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:29:56 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:41:29 - APIServer - INFO - API服务器已停止
2025-07-02 16:45:45 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-02 16:45:45 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-02 16:45:45 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-02 08:45:46 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-02 08:45:49 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:45:49 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:45:49 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:45:49 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:45:49 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:45:49 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:46:31 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:46:31 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:46:31 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:46:31 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:46:31 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:46:31 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 16:56:18 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-02 16:56:18 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-02 16:56:18 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-02 08:56:18 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-02 08:56:25 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:56:25 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:56:25 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:56:25 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:56:25 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:56:25 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 08:56:41 - APIServer - INFO - 向全体用户发送通知: 测试
2025-07-02 08:56:41 - APIServer - INFO - 通知已发送: 测试 - 全体用户
2025-07-02 08:56:51 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-02 08:56:51 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-02 08:56:51 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-02 09:02:47 - APIServer - INFO - API服务器已停止
