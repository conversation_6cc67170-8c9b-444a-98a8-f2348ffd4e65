@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: 设置颜色代码
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

echo %BLUE%===================================================
echo 🚀 企业级文件共享系统 - 一键启动工具
echo ===================================================
echo.%RESET%

:: 检查Python环境
echo %YELLOW%[1/10]%RESET% 检查Python环境...
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%[错误]%RESET% 未检测到Python环境，请安装Python 3.7或更高版本。
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 获取Python版本并检查
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo %GREEN%✓%RESET% Python版本: %python_version%

:: 检查Python版本是否满足要求
python -c "import sys; exit(0 if sys.version_info >= (3, 7) else 1)" 2>nul
if %errorlevel% neq 0 (
    echo %RED%[错误]%RESET% Python版本过低，需要3.7或更高版本
    pause
    exit /b 1
)

:: 创建虚拟环境
echo %YELLOW%[2/10]%RESET% 检查虚拟环境...
if not exist "venv\" (
    echo 创建虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo %RED%[错误]%RESET% 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo %GREEN%✓%RESET% 虚拟环境创建成功
) else (
    echo %GREEN%✓%RESET% 虚拟环境已存在
)

:: 激活虚拟环境
echo %YELLOW%[3/10]%RESET% 激活虚拟环境...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo %RED%[错误]%RESET% 激活虚拟环境失败
    pause
    exit /b 1
)
echo %GREEN%✓%RESET% 虚拟环境已激活

:: 升级pip并安装依赖
echo %YELLOW%[4/10]%RESET% 安装依赖项(使用国内镜像源)...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple --upgrade pip >nul 2>&1
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r backend/requirements.txt >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%[错误]%RESET% 依赖安装失败，尝试重新安装...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r backend/requirements.txt
    if %errorlevel% neq 0 (
        echo %RED%[错误]%RESET% 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)
echo %GREEN%✓%RESET% 依赖安装完成

:: 检查数据库状态
echo %YELLOW%[5/10]%RESET% 检查数据库状态...
if not exist "backend\data\file_share_system.db" (
    echo 数据库不存在，正在初始化...
    cd backend
    python init_database_sqlite.py
    cd ..
    if %errorlevel% neq 0 (
        echo %RED%[错误]%RESET% 数据库初始化失败
        pause
        exit /b 1
    )
    echo %GREEN%✓%RESET% 数据库初始化完成
) else (
    echo %GREEN%✓%RESET% 数据库已存在
)

:: 检查端口占用
echo %YELLOW%[6/10]%RESET% 检查端口占用情况...
netstat -an | findstr ":8082" >nul 2>&1
if %errorlevel% equ 0 (
    echo %YELLOW%[警告]%RESET% 端口8082已被占用，请关闭占用该端口的程序
)
netstat -an | findstr ":8086" >nul 2>&1
if %errorlevel% equ 0 (
    echo %YELLOW%[警告]%RESET% 端口8086已被占用，请关闭占用该端口的程序
)

:: 启动后端服务
echo %YELLOW%[7/10]%RESET% 启动后端服务...
cd backend
start /b python main.py >nul 2>&1
cd ..
echo %GREEN%✓%RESET% 后端服务启动中...

:: 等待服务启动
echo %YELLOW%[8/10]%RESET% 等待服务启动完成...
timeout /t 3 /nobreak >nul

:: 检查服务健康状态
echo %YELLOW%[9/10]%RESET% 检查服务状态...
:: 简单的连接测试
ping 127.0.0.1 -n 1 >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%✓%RESET% 服务连接正常
) else (
    echo %YELLOW%[警告]%RESET% 服务可能未完全启动，请稍等片刻
)

:: 打开浏览器
echo %YELLOW%[10/10]%RESET% 启动浏览器...
timeout /t 2 /nobreak >nul
start http://localhost:8082
echo %GREEN%✓%RESET% 浏览器已打开

echo.
echo %GREEN%===================================================
echo 🎉 系统启动完成！
echo ===================================================
echo.
echo 📱 前端地址: http://localhost:8082
echo 🔌 API地址:  http://localhost:8086
echo 👤 管理员账户: admin / admin123
echo.
echo ⚠️  请保持此窗口打开以维持服务运行
echo ❌ 按 Ctrl+C 可停止服务
echo ===================================================
echo.%RESET%

:: 保持窗口打开
:wait_loop
timeout /t 60 /nobreak >nul
echo %BLUE%[%date% %time%]%RESET% 服务运行中...
goto wait_loop 