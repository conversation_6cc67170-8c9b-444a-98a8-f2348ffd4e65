#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务器启动脚本
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.insert(0, backend_dir)

# 切换到backend目录
os.chdir(backend_dir)

# 导入并启动API服务器
from api.server import APIServer

if __name__ == '__main__':
    try:
        # 创建API服务器实例
        api_server = APIServer()
        
        # 启动服务器
        print("正在启动API服务器...")
        api_server.run()
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动服务器时发生错误: {e}")
        import traceback
        traceback.print_exc()
