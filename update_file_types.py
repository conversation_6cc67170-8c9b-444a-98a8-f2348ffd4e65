#!/usr/bin/env python3
"""
更新数据库中所有文件的类型信息
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.file_share import SharedFile
from services.database_manager import DatabaseManager

def update_file_types():
    """更新所有文件的类型信息"""
    db_manager = DatabaseManager()
    
    with db_manager.get_session() as session:
        # 获取所有文件
        files = session.query(SharedFile).all()
        print(f"找到 {len(files)} 个文件需要更新")
        
        updated_count = 0
        for file in files:
            try:
                # 获取文件扩展名
                _, ext = os.path.splitext(file.filename)
                file.extension = ext.lower()
                
                # 判断文件类型
                image_exts = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.psd', '.ai', '.eps', '.svg'}
                video_exts = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'}
                doc_exts = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'}
                
                old_is_image = file.is_image
                file.is_image = file.extension in image_exts
                file.is_video = file.extension in video_exts
                file.is_document = file.extension in doc_exts
                
                if old_is_image != file.is_image:
                    print(f"更新文件 {file.filename}: extension={file.extension}, is_image={file.is_image}")
                    updated_count += 1
                
            except Exception as e:
                print(f"更新文件 {file.filename} 失败: {e}")
        
        # 提交更改
        session.commit()
        print(f"成功更新 {updated_count} 个文件的类型信息")

if __name__ == "__main__":
    update_file_types()
