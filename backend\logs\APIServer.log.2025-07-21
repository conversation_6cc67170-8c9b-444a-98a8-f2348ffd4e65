2025-07-21 15:23:37 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-21 15:23:37 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-21 15:23:37 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-21 07:23:38 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-21 07:23:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-21 07:23:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 17, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-11T08:29:44', 'updated_at': '2025-07-11T08:29:44', 'last_scanned': '2025-07-11T08:29:44.550757', 'file_count': 2}]}
2025-07-21 07:23:43 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-07-21 07:23:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-21 07:23:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 17, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-11T08:29:44', 'updated_at': '2025-07-11T08:29:44', 'last_scanned': '2025-07-11T08:29:44.550757', 'file_count': 2}]}
2025-07-21 07:23:43 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-07-21 07:23:43 - APIServer - INFO - 客户端连接: vNIWvsGZNsZi3SL_AAAB
2025-07-21 07:23:43 - APIServer - INFO - 客户端连接: h75VRO3yEBYaMiOLAAAD
2025-07-21 07:23:44 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1199
2025-07-21 07:23:44 - APIServer - INFO - 缩略图请求 - 文件ID: 1199, 文件信息: {'id': 1199, 'folder_id': 17, 'filename': '乡村振兴22.jpg', 'relative_path': '乡村振兴22.jpg', 'file_size': 4610118, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-03-18T10:22:47.411272', 'created_at': '2025-07-11T08:29:44', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\乡村振兴22.jpg', 'current_size': 4610118, 'current_modified': '2023-03-18T10:22:47.411272', 'exists': True}
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 17
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-21 07:23:44 - APIServer - INFO - 缩略图请求 - 文件名: 乡村振兴22.jpg
2025-07-21 07:23:44 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-21 07:23:44 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-21 07:23:44 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\乡村振兴22.jpg
2025-07-21 07:23:44 - APIServer - INFO - 缩略图服务可用: True
2025-07-21 07:23:44 - APIServer - INFO - 开始生成缩略图: C:\321\2\乡村振兴22.jpg, 尺寸: medium
2025-07-21 07:23:44 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-21 07:23:44 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-21 07:23:44 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-21 07:23:44 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1200
2025-07-21 07:23:44 - APIServer - INFO - 缩略图请求 - 文件ID: 1200, 文件信息: {'id': 1200, 'folder_id': 17, 'filename': '系列图1.jpg', 'relative_path': '系列图1.jpg', 'file_size': 3511876, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-09-25T09:54:08', 'created_at': '2025-07-11T08:29:44', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\系列图1.jpg', 'current_size': 3511876, 'current_modified': '2022-09-25T09:54:08', 'exists': True}
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 17
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-21 07:23:44 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-21 07:23:44 - APIServer - INFO - 缩略图请求 - 文件名: 系列图1.jpg
2025-07-21 07:23:44 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-21 07:23:44 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-21 07:23:44 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\系列图1.jpg
2025-07-21 07:23:44 - APIServer - INFO - 缩略图服务可用: True
2025-07-21 07:23:44 - APIServer - INFO - 开始生成缩略图: C:\321\2\系列图1.jpg, 尺寸: medium
2025-07-21 07:23:44 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-21 07:23:44 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-21 07:23:44 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-21 07:23:50 - APIServer - INFO - 客户端断开连接: h75VRO3yEBYaMiOLAAAD
2025-07-21 07:23:50 - APIServer - INFO - 客户端断开连接: vNIWvsGZNsZi3SL_AAAB
2025-07-21 07:26:13 - APIServer - INFO - API服务器已停止
