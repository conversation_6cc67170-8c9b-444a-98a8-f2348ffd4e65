#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API服务器用于测试CORS
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import json

app = Flask(__name__)

# 配置CORS - 允许所有来源
CORS(app,
     origins="*",  # 允许所有来源
     allow_headers=["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin"],
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
     supports_credentials=True,
     expose_headers=["Content-Type", "Authorization"])

@app.route('/api/test', methods=['GET', 'POST', 'OPTIONS'])
def test():
    """测试端点"""
    if request.method == 'OPTIONS':
        # 处理预检请求
        return '', 200
    
    return jsonify({
        'success': True,
        'message': 'CORS测试成功',
        'method': request.method,
        'headers': dict(request.headers)
    })

@app.route('/api/files/upload', methods=['POST', 'OPTIONS'])
def upload():
    """模拟文件上传端点"""
    if request.method == 'OPTIONS':
        # 处理预检请求
        return '', 200
    
    return jsonify({
        'success': True,
        'message': '文件上传测试成功',
        'files': list(request.files.keys()) if request.files else [],
        'form_data': dict(request.form)
    })

@app.route('/api/folders', methods=['GET', 'OPTIONS'])
def folders():
    """模拟文件夹列表端点"""
    if request.method == 'OPTIONS':
        return '', 200
    
    return jsonify({
        'success': True,
        'folders': [
            {'id': 1, 'name': '测试文件夹1', 'path': '/test1'},
            {'id': 2, 'name': '测试文件夹2', 'path': '/test2'}
        ]
    })

if __name__ == '__main__':
    print("启动简单API服务器...")
    print("服务器地址: http://localhost:8086")
    print("测试端点: http://localhost:8086/api/test")
    app.run(host='0.0.0.0', port=8086, debug=True)
