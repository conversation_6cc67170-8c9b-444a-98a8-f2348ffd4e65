2025-07-03 08:05:17 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-03 08:05:17 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-03 08:05:17 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-03 00:05:18 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-03 00:06:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-03 00:06:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-03 00:06:04 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-03 00:06:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-03 00:06:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-03 00:06:04 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-03 00:06:05 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1194
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件ID: 1194, 文件信息: {'id': 1194, 'folder_id': 15, 'filename': '0ad4b2c9b604d426 [转换].jpg', 'relative_path': '0ad4b2c9b604d426 [转换].jpg', 'file_size': 2587497, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2024-04-26T02:03:45.702794', 'created_at': '2025-07-02T07:23:35', 'last_accessed': None}, 'full_path': 'C:\\321\\0ad4b2c9b604d426 [转换].jpg', 'current_size': 2587497, 'current_modified': '2024-04-26T02:03:45.702794', 'exists': True}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 15
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件名: 0ad4b2c9b604d426 [转换].jpg
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\0ad4b2c9b604d426 [转换].jpg
2025-07-03 00:06:05 - APIServer - INFO - 缩略图服务可用: True
2025-07-03 00:06:05 - APIServer - INFO - 开始生成缩略图: C:\321\0ad4b2c9b604d426 [转换].jpg, 尺寸: medium
2025-07-03 00:06:05 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\13fd8841a29c03f9ebc5c9fb71522397_medium.jpg
2025-07-03 00:06:05 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\13fd8841a29c03f9ebc5c9fb71522397_medium.jpg
2025-07-03 00:06:05 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\13fd8841a29c03f9ebc5c9fb71522397_medium.jpg
2025-07-03 00:06:05 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1196
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件ID: 1196, 文件信息: {'id': 1196, 'folder_id': 15, 'filename': '系列图1.jpg', 'relative_path': '2\\系列图1.jpg', 'file_size': 3511876, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-09-25T09:54:08', 'created_at': '2025-07-02T07:23:35', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\系列图1.jpg', 'current_size': 3511876, 'current_modified': '2022-09-25T09:54:08', 'exists': True}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 15
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件名: 系列图1.jpg
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\系列图1.jpg
2025-07-03 00:06:05 - APIServer - INFO - 缩略图服务可用: True
2025-07-03 00:06:05 - APIServer - INFO - 开始生成缩略图: C:\321\2\系列图1.jpg, 尺寸: medium
2025-07-03 00:06:05 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-03 00:06:05 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-03 00:06:05 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-03 00:06:05 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1195
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件ID: 1195, 文件信息: {'id': 1195, 'folder_id': 15, 'filename': '乡村振兴22.jpg', 'relative_path': '2\\乡村振兴22.jpg', 'file_size': 4610118, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-03-18T10:22:47.411272', 'created_at': '2025-07-02T07:23:35', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\乡村振兴22.jpg', 'current_size': 4610118, 'current_modified': '2023-03-18T10:22:47.411272', 'exists': True}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 15
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-03 00:06:05 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件名: 乡村振兴22.jpg
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-03 00:06:05 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\乡村振兴22.jpg
2025-07-03 00:06:05 - APIServer - INFO - 缩略图服务可用: True
2025-07-03 00:06:05 - APIServer - INFO - 开始生成缩略图: C:\321\2\乡村振兴22.jpg, 尺寸: medium
2025-07-03 00:06:05 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-03 00:06:05 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-03 00:06:05 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-03 00:08:32 - APIServer - INFO - 向全体用户发送通知: 111
2025-07-03 00:08:32 - APIServer - INFO - 通知已发送: 111 - 全体用户
2025-07-03 00:08:44 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-03 00:08:44 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-03 00:08:44 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-03 00:08:44 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-03 00:08:44 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 13, 'name': '123', 'path': 'C:\\123', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 29, 'total_size': 466370095, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-01T08:48:51', 'updated_at': '2025-07-01T08:48:51', 'last_scanned': '2025-07-01T16:48:51.204635', 'file_count': 29}, {'id': 15, 'name': '321', 'path': 'C:\\321', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 10709491, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-02T07:23:35', 'updated_at': '2025-07-02T07:23:35', 'last_scanned': '2025-07-02T07:23:35.242309', 'file_count': 3}]}
2025-07-03 00:08:44 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-03 00:08:44 - APIServer - INFO - 客户端连接: MFFKo94-r4YmYAOtAAAC
2025-07-03 00:08:44 - APIServer - INFO - 客户端连接: Nh36QMka2z9pmBEaAAAE
2025-07-03 00:10:07 - APIServer - INFO - API服务器已停止
