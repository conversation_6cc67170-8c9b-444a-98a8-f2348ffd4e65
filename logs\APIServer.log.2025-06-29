2025-06-29 20:05:45 - APIServer - INFO - 网络访问控制服务初始化成功
2025-06-29 20:05:45 - APIServer - INFO - 用户行为监控服务初始化成功
2025-06-29 20:05:45 - APIServer - INFO - CORS已配置为允许局域网访问
2025-06-29 20:05:46 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-29 20:05:46 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-29 20:05:46 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 3, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 104857600, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 69003, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-29T04:33:13', 'updated_at': '2025-06-29T09:43:18', 'last_scanned': '2025-06-29T12:33:17.784373', 'file_count': 3}]}
2025-06-29 20:05:46 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-06-29 20:06:05 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-29 20:06:05 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 3, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 104857600, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 69003, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-29T04:33:13', 'updated_at': '2025-06-29T09:43:18', 'last_scanned': '2025-06-29T12:33:17.784373', 'file_count': 3}]}
2025-06-29 20:06:05 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-06-29 20:06:05 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-29 20:06:05 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 3, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 104857600, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 69003, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-29T04:33:13', 'updated_at': '2025-06-29T09:43:18', 'last_scanned': '2025-06-29T12:33:17.784373', 'file_count': 3}]}
2025-06-29 20:06:05 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-06-29 20:06:46 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 324
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件ID: 324, 文件信息: {'id': 324, 'folder_id': 3, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': '.jpeg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:48:59.585574', 'created_at': '2025-06-29T04:33:13', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 3
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-29 20:06:46 - APIServer - INFO - 缩略图服务可用: True
2025-06-29 20:06:46 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-29 20:06:46 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-29 20:06:46 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-29 20:06:46 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-29 20:06:46 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 326
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件ID: 326, 文件信息: {'id': 326, 'folder_id': 3, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '测试8\\5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': '.jpeg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:48:59.585574', 'created_at': '2025-06-29T04:33:13', 'last_accessed': None}, 'full_path': 'D:\\测试\\测试8\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 3
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\测试8\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-29 20:06:46 - APIServer - INFO - 缩略图服务可用: True
2025-06-29 20:06:46 - APIServer - INFO - 开始生成缩略图: D:\测试\测试8\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-29 20:06:46 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f9a297312a4f5faae9968b89c1fa34ec_medium.jpg
2025-06-29 20:06:46 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f9a297312a4f5faae9968b89c1fa34ec_medium.jpg
2025-06-29 20:06:46 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f9a297312a4f5faae9968b89c1fa34ec_medium.jpg
2025-06-29 20:06:46 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 325
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件ID: 325, 文件信息: {'id': 325, 'folder_id': 3, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23.199260', 'created_at': '2025-06-29T04:33:13', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 3
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-06-29 20:06:46 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-29 20:06:46 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-29 20:06:46 - APIServer - INFO - 缩略图服务可用: True
2025-06-29 20:06:46 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-29 20:06:46 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-29 20:06:46 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-29 20:06:46 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-29 20:06:52 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-29 20:06:52 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 3, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 104857600, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 69003, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-29T04:33:13', 'updated_at': '2025-06-29T09:43:18', 'last_scanned': '2025-06-29T12:33:17.784373', 'file_count': 3}]}
2025-06-29 20:06:52 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-06-29 20:06:52 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-29 20:06:52 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 3, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 104857600, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 69003, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-29T04:33:13', 'updated_at': '2025-06-29T09:43:18', 'last_scanned': '2025-06-29T12:33:17.784373', 'file_count': 3}]}
2025-06-29 20:06:52 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-06-29 20:06:53 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-29 20:06:53 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 3, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 104857600, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 69003, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-29T04:33:13', 'updated_at': '2025-06-29T09:43:18', 'last_scanned': '2025-06-29T12:33:17.784373', 'file_count': 3}]}
2025-06-29 20:06:53 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-06-29 20:06:55 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-29 20:06:55 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 3, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 104857600, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 69003, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-29T04:33:13', 'updated_at': '2025-06-29T09:43:18', 'last_scanned': '2025-06-29T12:33:17.784373', 'file_count': 3}]}
2025-06-29 20:06:55 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-06-29 20:07:10 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-29 20:07:10 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 3, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 104857600, 'allowed_extensions': None}, 'statistics': {'file_count': 3, 'total_size': 69003, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-29T04:33:13', 'updated_at': '2025-06-29T09:43:18', 'last_scanned': '2025-06-29T12:33:17.784373', 'file_count': 3}]}
2025-06-29 20:07:10 - APIServer - INFO - 返回 1 个有权限的文件夹
2025-06-29 20:12:33 - APIServer - INFO - API服务器已停止
